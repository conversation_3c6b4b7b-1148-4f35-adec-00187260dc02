<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Only check installation for HTTP requests, not console commands
        if (!app()->runningInConsole() &&
            !file_exists(storage_path('installed')) &&
            !request()->is('install') &&
            !request()->is('install/*')) {
            header("Location: install/");
            exit;
        }
    }
}
