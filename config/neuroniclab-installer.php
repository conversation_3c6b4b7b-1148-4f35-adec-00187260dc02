<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Server Requirements
    |--------------------------------------------------------------------------
    |
    | This is the default Laravel server requirements, you can add as many
    | as your application require, we check if the extension is enabled
    | by looping through the array and run "extension_loaded" on it.
    |
    */
    'core' => [
        'minPhpVersion' => '8.1',
    ],
    
    'final' => [
        'key' => true,
        'publish' => false,
    ],
    
    'requirements' => [
        'php' => [
            'openssl',
            'pdo',
            'mbstring',
            'tokenizer',
            'JSON',
            'cURL',
            'fileinfo',
            'gd',
            'zip',
            'bcmath',
            'exif',
            'imap',
        ],
        'apache' => [
            'mod_rewrite',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Folders Permissions
    |--------------------------------------------------------------------------
    |
    | This is the default Laravel folders permissions, if your application
    | requires more permissions just add them to the array list below.
    |
    */
    'permissions' => [
        'storage/framework/'     => '775',
        'storage/logs/'          => '775',
        'storage/app/'          => '775',
        'bootstrap/cache/'       => '775',
        'public/sitemaps/'       => '775',
        'public/files/'          => '775',
        'resources/lang/'        => '775'
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment Form Wizard Validation Rules & Messages
    |--------------------------------------------------------------------------
    |
    | This are the default form field validation rules. Available Rules:
    | https://laravel.com/docs/validation#available-validation-rules
    |
    */
    'environment' => [
        'form' => [
            'rules' => [
                'app_name'              => 'required|string|max:50',
                'app_debug'             => 'required|string',
                'app_url'               => 'required|url',
                'database_hostname'     => 'required|string|max:50',
                'database_name'         => 'required|string|max:50',
                'database_username'     => 'required|string|max:50',
                'database_password'     => 'nullable|string|max:50',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | License Configuration
    |--------------------------------------------------------------------------
    |
    | Configure license verification settings for the installer.
    |
    */
    'license' => [
        'enabled' => false, // Disabled for development
        'verification_file' => 'vendor/mockery/mockery/verified',
        'verification_url' => null, // Set to your license verification endpoint
        'item_id' => null,
        'item_name' => 'Laravel React Starter Kit',
        'required_fields' => [
            'email' => true,
            'username' => true,
            'purchase_code' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    |
    | Configure database import settings.
    |
    */
    'database' => [
        'schema_file' => 'database/neuroniclab-installer/schema.sql',
        'mysql_path' => 'mysql', // Path to MySQL binary
        'import_method' => 'auto', // 'mysql', 'php', or 'auto'
    ],

    /*
    |--------------------------------------------------------------------------
    | Installed Middleware Options
    |--------------------------------------------------------------------------
    | Different available status switch configuration for the
    | canInstall middleware.
    |
    */
    'installed' => [
        'file_path' => 'storage/installed',
        'redirectOptions' => [
            'route' => [
                'name' => 'welcome',
                'data' => [],
            ],
            'abort' => [
                'type' => '404',
            ],
            'dump' => [
                'data' => 'Application already installed.',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Selected Installed Middleware Option
    |--------------------------------------------------------------------------
    | The selected option for what happens when an installer instance has been
    | already completed. Available options include:
    | route, abort, dump, 404, default, ''
    |
    */
    'installedAlreadyAction' => '',

    /*
    |--------------------------------------------------------------------------
    | Updater Enabled
    |--------------------------------------------------------------------------
    | Can the application run the '/update' route with the migrations.
    | The default option is set to False if none is present.
    | Boolean value
    |
    */
    'updaterEnabled' => 'true',

    /*
    |--------------------------------------------------------------------------
    | UI Customization
    |--------------------------------------------------------------------------
    |
    | Customize the installer UI appearance.
    |
    */
    'ui' => [
        'app_name' => 'Laravel React Starter Kit',
        'logo' => 'logo.svg', // Path to logo image
        'primary_color' => '#3b82f6',
        'background_image' => null,
        'custom_css' => null,
    ],

    /*
    |--------------------------------------------------------------------------
    | Installation Steps
    |--------------------------------------------------------------------------
    |
    | Define which steps should be included in the installation process.
    |
    */
    'steps' => [
        'welcome' => true,
        'requirements' => true,
        'permissions' => true,
        'license' => true,
        'environment' => true,
        'database' => true,
        'final' => true,
    ],

];
