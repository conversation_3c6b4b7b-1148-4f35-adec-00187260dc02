# NeuronicLab Installer - Quick Reference

## Quick Start

1. **Start the application**:
   ```bash
   php artisan serve
   ```

2. **Access installer**: Navigate to `http://localhost:8000`

3. **Follow the wizard**:
   - Welcome → Requirements → Permissions → Environment → Database → Complete

## Key Files

- **Configuration**: `config/neuroniclab-installer.php`
- **Database Schema**: `database/neuroniclab-installer/schema.sql`
- **Installation Check**: `app/Providers/AppServiceProvider.php`
- **Assets**: `public/neuroniclab-installer/`

## Important Routes

- `/install` - Main installer
- `/install/requirements` - System check
- `/install/environment/wizard` - Database setup
- `/update` - Application updater

## Configuration Options

### Disable License Verification
```php
'license' => ['enabled' => false]
```

### Customize UI
```php
'ui' => [
    'app_name' => 'Your App Name',
    'logo' => 'path/to/logo.png',
    'primary_color' => '#your-color'
]
```

### Database Settings
```php
'database' => [
    'schema_file' => 'database/neuroniclab-installer/schema.sql',
    'mysql_path' => 'mysql'
]
```

## Commands

```bash
# Republish assets
php artisan neuroniclab:publish --tag=assets --force

# Check routes
php artisan route:list --path=install

# Clear caches
php artisan route:clear
php artisan config:clear
```

## Troubleshooting

- **Permission errors**: `chmod -R 775 storage bootstrap/cache`
- **Assets not loading**: Republish with `--force` flag
- **Route conflicts**: Clear route cache
- **Debug mode**: Set `APP_DEBUG=true` in `.env`

## After Installation

- `storage/installed` file is created
- Installer routes are automatically blocked
- Application is ready for normal use

## Security Notes

- Remove installer in production if desired
- Use HTTPS for license verification
- Set proper file permissions
- Review and secure configuration files
