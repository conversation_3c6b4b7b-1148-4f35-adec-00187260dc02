feat: integrate NeuronicLab installer package for web-based application setup

- Add NeuronicLab installer as local Composer path repository
- Update installer dependencies to support Laravel 12.x and Carbon 3.x
- Publish installer assets, views, configuration, and database schema
- Configure AppServiceProvider to redirect to installer when not installed
- Customize installer U<PERSON> with application branding and colors
- Create basic Laravel-compatible database schema for installation
- Add missing UpdateController to resolve route registration issues
- Disable license verification for development environment
- Document complete integration process and usage instructions

The installer provides:
- Beautiful responsive UI with step-by-step installation wizard
- System requirements and permissions validation
- Automated database setup with MySQL/MariaDB support
- Environment configuration wizard
- Optional license verification capability
- Automatic redirection and installation state management

All installer routes are properly registered and functional.
Assets load correctly and no server errors are present.
Integration tested and verified working on Laravel 12.x with React/Inertia.js frontend.
