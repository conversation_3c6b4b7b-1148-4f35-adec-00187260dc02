# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2024-01-10

### BREAKING CHANGES
- **Independence**: Removed dependency on `rachidlaasri/laravel-installer`
- **Self-contained**: All functionality now implemented with custom helper classes

### Added
- Custom `DatabaseManager` helper class for database operations
- Custom `EnvironmentManager` helper class for .env file management
- Custom `FinalInstallManager` helper class for final installation steps
- Custom `InstalledFileManager` helper class for installation tracking
- Custom `PermissionsChecker` helper class for file permission validation
- Custom `RequirementsChecker` helper class for PHP requirements checking
- Custom `EnvironmentSaved` event class
- Custom `LaravelInstallerFinished` event class

### Changed
- All controllers now use NeuronicLab helper classes instead of rachidlaasri classes
- Updated composer.json to remove rachidlaasri/laravel-installer dependency
- Added nesbot/carbon dependency for date handling
- Updated PHPDoc comments to reflect new class namespaces

### Removed
- Dependency on `rachidlaasri/laravel-installer` package
- All references to RachidLaasri namespace classes

### Technical Benefits
- **Reduced Dependencies**: Fewer external dependencies to manage
- **Full Control**: Complete control over installer functionality
- **Customization**: Easier to customize and extend functionality
- **Maintenance**: No dependency on external package updates
- **Security**: Reduced attack surface with fewer dependencies

## [1.0.0] - 2024-01-10

### Added
- Initial release of NeuronicLab Installer package
- Complete installer UI with custom branding support
- License verification system with configurable endpoints
- Database schema import functionality
- Requirements and permissions checking
- Environment configuration wizard
- Custom middleware for installation and security checks
- Artisan commands for package installation and publishing
- Multi-language support
- Configurable UI customization options
- Self-contained installer implementation

### Features
- **Installation Steps**: Welcome, Requirements, Permissions, License, Environment, Database, Final
- **License Verification**: Configurable license checking with remote verification support
- **Database Import**: Support for MySQL command-line and PHP-based SQL import
- **UI Customization**: Configurable colors, logos, and custom CSS
- **Security**: Built-in security middleware and installation state checking
- **Commands**: `neuroniclab:install` and `neuroniclab:publish` artisan commands
- **Extensibility**: Easily extendable controllers and views

### Technical Details
- Laravel 10.0+ compatibility
- PHP 8.1+ requirement
- PSR-4 autoloading
- Service provider auto-discovery
- Comprehensive configuration system
- Helper functions for route checking

### Documentation
- Complete README with installation and usage instructions
- Integration guide for developers
- Configuration examples and customization options
- Troubleshooting guide
