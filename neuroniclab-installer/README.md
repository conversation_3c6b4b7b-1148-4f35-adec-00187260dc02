# NeuronicLab Installer

A comprehensive Laravel installer package that provides a complete installation experience with custom UI, license verification, and database setup capabilities.

## Features

- **Custom Installation UI**: Beautiful, branded installer interface
- **License Verification**: Built-in license validation system
- **Database Setup**: Automated database schema installation
- **Requirements Checking**: PHP extensions and server requirements validation
- **Permission Validation**: File and directory permissions checking
- **Environment Configuration**: Guided environment setup
- **Security Integration**: Built-in security middleware
- **Multi-language Support**: Internationalization ready

## Installation

1. Install the package via Composer:

```bash
composer require neuroniclab/installer
```

2. Install the package using the artisan command:

```bash
php artisan neuroniclab:install
```

Or manually publish the package assets and configuration:

```bash
php artisan vendor:publish --provider="NeuronicLab\Installer\Providers\InstallerServiceProvider"
```

3. Configure your installer settings in `config/neuroniclab-installer.php`

## Usage

Once installed, users can access the installer at `/install` in their browser.

### Quick Start

1. After installation, configure your database schema file path in the config
2. Customize the license verification settings if needed
3. Modify the UI settings to match your brand
4. Access `/install` to start the installation process

### Artisan Commands

- `php artisan neuroniclab:install` - Install the package with all assets
- `php artisan neuroniclab:publish --tag=config` - Publish only configuration
- `php artisan neuroniclab:publish --tag=assets` - Publish only assets
- `php artisan neuroniclab:publish --tag=views` - Publish only views

### Configuration

The installer can be customized through the `config/neuroniclab-installer.php` file:

#### Database Configuration
```php
'database' => [
    'schema_file' => 'database/neuroniclab-installer/schema.sql',
    'mysql_path' => 'mysql',
    'import_method' => 'auto',
],
```

#### License Configuration
```php
'license' => [
    'enabled' => true,
    'verification_file' => 'vendor/mockery/mockery/verified',
    'verification_url' => null, // Your license verification endpoint
    'item_id' => null,
    'item_name' => 'NeuronicLab',
],
```

#### UI Customization
```php
'ui' => [
    'app_name' => 'NeuronicLab',
    'logo' => null,
    'primary_color' => '#007bff',
    'background_image' => null,
    'custom_css' => null,
],
```

### Customization

You can customize the installer by:

1. **Publishing and modifying the views**:
   ```bash
   php artisan neuroniclab:publish --tag=views
   ```

2. **Overriding the CSS and assets**:
   ```bash
   php artisan neuroniclab:publish --tag=assets
   ```

3. **Extending the controllers**: Create your own controllers that extend the package controllers

4. **Adding custom validation rules**: Modify the configuration file to add custom requirements

### Advanced Usage

#### Custom License Verification

To implement custom license verification:

1. Set up your license verification endpoint
2. Configure the verification URL in the config file
3. Implement your license validation logic

#### Custom Database Schema

To use a custom database schema:

1. Place your SQL file in the configured location
2. Update the schema file path in the configuration
3. Ensure proper MySQL path configuration for command-line import

#### Middleware Integration

The package provides middleware for:
- Installation checking (`CanInstall`)
- Security verification (`Security`)

Register these in your application's middleware as needed.

## Requirements

- PHP 8.1 or higher
- Laravel 10.0 or higher
- MySQL/MariaDB database

## License

This package is open-sourced software licensed under the [MIT license](LICENSE).
