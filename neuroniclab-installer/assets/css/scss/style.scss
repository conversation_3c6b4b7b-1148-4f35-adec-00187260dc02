// Variables
@import "variables";

// Font Awesome
@import "font-awesome/font-awesome";

//@extend-elements
//original selectors
//sub, sup
%extend_1 {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

//original selectors
//button, input, optgroup, select, textarea
%extend_2 {
	color: inherit;
	font: inherit;
	margin: 0;
}

@import url($url_0);
html {
	font-family: $font_0;
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
	font-family: $font_2, $font_3, $font_4, $font_5, $font_0;
	font-weight: 300;
	color: $color_3;
	font-size: 12px;
	line-height: 1.75em;
	input[type=button] {
		-webkit-appearance: button;
		cursor: pointer;
	}
	input[disabled] {
		cursor: default;
	}
}
body {
	margin: 0;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	-moz-font-feature-settings: "liga" on;
}
article {
	display: block;
}
aside {
	display: block;
}
details {
	display: block;
}
figcaption {
	display: block;
}
figure {
	display: block;
	margin: 1em 40px;
}
footer {
	display: block;
}
header {
	display: block;
}
hgroup {
	display: block;
}
main {
	display: block;
}
menu {
	display: block;
}
nav {
	display: block;
}
section {
	display: block;
}
summary {
	display: block;
}
audio {
	display: inline-block;
	vertical-align: baseline;
	&:not([controls]) {
		display: none;
		height: 0;
	}
}
canvas {
	display: inline-block;
	vertical-align: baseline;
}
progress {
	display: inline-block;
	vertical-align: baseline;
}
video {
	display: inline-block;
	vertical-align: baseline;
}
[hidden] {
	display: none;
}
template {
	display: none;
}
a {
	background-color: transparent;
	text-decoration: none;
	color: $color_5;
	//Instead of the line below you could use @include transition($transition-1, $transition-2, $transition-3, $transition-4, $transition-5, $transition-6, $transition-7, $transition-8, $transition-9, $transition-10)
	transition: all .2s;
	margin: 0;
	padding: 0;
	cursor: pointer;
	&:active {
		outline: 0;
	}
	&:hover {
		outline: 0;
		color: $color_6;
	}
}
abbr[title] {
	border-bottom: 1px dotted;
}
b {
	font-weight: 700;
	margin: 0;
	padding: 0;
}
strong {
	font-weight: 700;
	margin: 0;
	padding: 0;
}
dfn {
	font-style: italic;
	margin: 0;
	padding: 0;
}
h1 {
	font-size: 2em;
	margin: .67em 0;
	margin-top: .942400822452556em;
	line-height: 1.130880986943067em;
	margin-bottom: .188480164490511em;
	margin: 0;
	padding: 0;
	font-family: $font_2, $font_3, $font_4, $font_5, $font_0;
	font-weight: 500;
	color: $color_4;
	clear: both;
}
mark {
	background: $color_0;
	color: $color_1;
}
small {
	font-size: 80%;
	margin: 0;
	padding: 0;
	line-height: 0;
}
sub {
	@extend %extend_1;
	bottom: -.25em;
	margin: 0;
	padding: 0;
	line-height: 0;
}
sup {
	@extend %extend_1;
	top: -.5em;
	margin: 0;
	padding: 0;
	line-height: 0;
	a .fa {
		font-size: 1em;
	}
}
img {
	border: 0;
	margin: 0;
	padding: 0;
}
hr {
	//Instead of the line below you could use @include box-sizing($bs)
	box-sizing: content-box;
	height: 0;
}
pre {
	overflow: auto;
	padding: .875em;
	margin-bottom: 1.75em;
	background: $color_18;
	line-height: 1;
	color: $color_30;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 3px;
	font-size: 10px;
	font-family: $font_1;
	font-size: 1em;
	margin: 0;
	padding: 0;
	margin-bottom: 1.75em;
	code {
		padding: 0;
	}
}
code {
	font-family: $font_1;
	font-size: 1em;
	margin: 0;
	padding: 0;
	font-family: $font_6, $font_7, $font_8, $font_9, $font_1;
	padding: .0875em .2625em;
	line-height: 0;
}
kbd {
	font-family: $font_1;
	font-size: 1em;
	margin: 0;
	padding: 0;
}
samp {
	font-family: $font_1;
	font-size: 1em;
	margin: 0;
	padding: 0;
}
button {
	@extend %extend_2;
	overflow: visible;
	text-transform: none;
	-webkit-appearance: button;
	cursor: pointer;
	display: block;
	cursor: pointer;
	font-size: 12px;
	padding: .4375em 1.75em;
	margin-bottom: 1.18125em;
}
input {
	@extend %extend_2;
	line-height: normal;
	&:focus {
		background: $color_30;
	}
}
optgroup {
	@extend %extend_2;
	font-weight: 700;
}
select {
	@extend %extend_2;
	text-transform: none;
	outline: none;
	border: 1px solid $color_27;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 3px;
	padding: 10px 12px;
	width: calc(100% - 24px);
	margin: 0 auto 1em;
	width: 100%;
	height: 35px;
}
textarea {
	@extend %extend_2;
	overflow: auto;
	display: block;
	max-width: 100%;
	padding: .4375em;
	font-size: 12px;
	margin-bottom: 1.18125em;
	outline: none;
	border: 1px solid $color_27;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 3px;
	padding: 10px 12px;
	width: calc(100% - 24px);
	margin: 0 auto 1em;
}
input[type=reset] {
	-webkit-appearance: button;
	cursor: pointer;
}
input[type=submit] {
	-webkit-appearance: button;
	cursor: pointer;
	display: block;
	cursor: pointer;
	font-size: 12px;
	padding: .4375em 1.75em;
	margin-bottom: 1.18125em;
}
button[disabled] {
	cursor: default;
}
button::-moz-focus-inner {
	border: 0;
	padding: 0;
}
input::-moz-focus-inner {
	border: 0;
	padding: 0;
}
input[type=checkbox] {
	//Instead of the line below you could use @include box-sizing($bs)
	box-sizing: border-box;
	padding: 0;
}
input[type=radio] {
	//Instead of the line below you could use @include box-sizing($bs)
	box-sizing: border-box;
	padding: 0;
}
input[type=number]::-webkit-inner-spin-button {
	height: auto;
}
input[type=number]::-webkit-outer-spin-button {
	height: auto;
}
input[type=search] {
	-webkit-appearance: textfield;
	//Instead of the line below you could use @include box-sizing($bs)
	box-sizing: content-box;
}
input[type=search]::-webkit-search-cancel-button {
	-webkit-appearance: none;
}
input[type=search]::-webkit-search-decoration {
	-webkit-appearance: none;
}
fieldset {
	border: 1px solid $color_2;
	margin: 0 2px;
	padding: .35em .625em .75em;
	padding: .875em 1.75em 1.75em;
	border-width: 1px;
	border-style: solid;
	max-width: 100%;
	margin-bottom: 1.8375em;
	margin: 0;
	padding: 0;
	button {
		margin-bottom: 0;
	}
	input[type=submit] {
		margin-bottom: 0;
	}
}
legend {
	border: 0;
	padding: 0;
	color: $color_4;
	font-weight: 700;
	margin: 0;
	padding: 0;
}
table {
	width: 100%;
	border-spacing: 0;
	border-collapse: collapse;
	margin-bottom: 2.1875em;
	margin: 0;
	padding: 0;
	margin-bottom: 1.75em;
}
td {
	padding: 0;
	margin: 0;
	padding: 0;
	padding: .21875em .875em;
}
th {
	padding: 0;
	margin: 0;
	padding: 0;
	text-align: left;
	color: $color_4;
	padding: .21875em .875em;
}
@media(min-width:600px) {
	html {
		font-size: 12px;
	}
	h1 {
		font-size: calc(27.85438995234061px +18.56959 *((100vw - 600px) / 540));
	}
	h2 {
		font-size: calc(23.53700340860508px +15.69134 *((100vw - 600px) / 540));
	}
	h3 {
		font-size: calc(19.888804974891777px +13.2592 *((100vw - 600px) / 540));
	}
	h4 {
		font-size: calc(16.806071548796314px +11.20405 *((100vw - 600px) / 540));
	}
	h5 {
		font-size: calc(14.201156945318074px +9.46744 *((100vw - 600px) / 540));
	}
	h6 {
		font-size: calc(12px +8 *((100vw - 600px) / 540));
	}
}
abbr {
	margin: 0;
	padding: 0;
	border-bottom: 1px dotted currentColor;
	cursor: help;
}
acronym {
	margin: 0;
	padding: 0;
	border-bottom: 1px dotted currentColor;
	cursor: help;
}
address {
	margin: 0;
	padding: 0;
	margin-bottom: 1.75em;
	font-style: normal;
}
big {
	margin: 0;
	padding: 0;
	line-height: 0;
}
blockquote {
	margin: 0;
	padding: 0;
	margin-bottom: 1.75em;
	font-style: italic;
	cite {
		display: block;
		font-style: normal;
	}
}
caption {
	margin: 0;
	padding: 0;
}
center {
	margin: 0;
	padding: 0;
}
cite {
	margin: 0;
	padding: 0;
}
dd {
	margin: 0;
	padding: 0;
}
del {
	margin: 0;
	padding: 0;
}
dl {
	margin: 0;
	padding: 0;
	margin-bottom: 1.75em;
}
dt {
	margin: 0;
	padding: 0;
	color: $color_4;
	font-weight: 700;
}
em {
	margin: 0;
	padding: 0;
}
form {
	margin: 0;
	padding: 0;
}
h2 {
	margin: 0;
	padding: 0;
	font-family: $font_2, $font_3, $font_4, $font_5, $font_0;
	font-weight: 500;
	color: $color_4;
	clear: both;
	font-size: 23.53700340860508px;
	margin-top: 1.115265165420465em;
	line-height: 1.338318198504558em;
	margin-bottom: .251483121980101em;
}
h3 {
	margin: 0;
	padding: 0;
	font-family: $font_2, $font_3, $font_4, $font_5, $font_0;
	font-weight: 500;
	color: $color_4;
	clear: both;
	font-size: 19.888804974891777px;
	margin-top: 1.319837970815179em;
	line-height: 1.583805564978215em;
	margin-bottom: .303784103173448em;
}
h4 {
	margin: 0;
	padding: 0;
	font-family: $font_2, $font_3, $font_4, $font_5, $font_0;
	font-weight: 500;
	color: $color_4;
	clear: both;
	font-size: 16.806071548796314px;
	margin-top: 1.561935513828041em;
	line-height: 1.87432261659365em;
	margin-bottom: .368150361036632em;
}
h5 {
	margin: 0;
	padding: 0;
	font-family: $font_2, $font_3, $font_4, $font_5, $font_0;
	font-weight: 500;
	color: $color_4;
	clear: both;
	font-size: 14.201156945318074px;
	margin-top: 1.84844094752817em;
	line-height: 2.218129137033805em;
	margin-bottom: .369688189505634em;
}
h6 {
	margin: 0;
	padding: 0;
	font-family: $font_2, $font_3, $font_4, $font_5, $font_0;
	font-weight: 500;
	color: $color_4;
	clear: both;
	font-size: 12px;
	margin-top: 2.1875em;
	line-height: 2.625em;
	margin-bottom: .619791666666667em;
}
i {
	margin: 0;
	padding: 0;
}
ins {
	margin: 0;
	padding: 0;
}
label {
	margin: 0;
	padding: 0;
	display: block;
	padding-bottom: .21875em;
	margin-bottom: -.21875em;
	cursor: pointer;
}
li {
	margin: 0;
	padding: 0;
}
ol {
	margin: 0;
	padding: 0;
	margin-bottom: 1.75em;
	padding-left: 1.4em;
}
p {
	margin: 0;
	padding: 0;
	margin-bottom: 1.75em;
	margin: 0 0 1rem 0;
}
q {
	margin: 0;
	padding: 0;
}
s {
	margin: 0;
	padding: 0;
}
strike {
	margin: 0;
	padding: 0;
}
tbody {
	margin: 0;
	padding: 0;
}
tfoot {
	margin: 0;
	padding: 0;
}
thead {
	margin: 0;
	padding: 0;
}
tr {
	margin: 0;
	padding: 0;
}
tt {
	margin: 0;
	padding: 0;
}
u {
	margin: 0;
	padding: 0;
}
ul {
	margin: 0;
	padding: 0;
	margin-bottom: 1.75em;
	padding-left: 1.1em;
}
var {
	margin: 0;
	padding: 0;
}
@media(min-width:1140px) {
	h1 {
		font-size: 46.423983253901014px;
		margin-top: .942400822452556em;
		line-height: 1.130880986943067em;
		margin-bottom: .188480164490511em;
	}
	h2 {
		font-size: 39.228339014341806px;
		margin-top: 1.115265165420465em;
		line-height: 1.338318198504558em;
		margin-bottom: .240111086421698em;
	}
	h3 {
		font-size: 33.14800829148629px;
		margin-top: 1.319837970815179em;
		line-height: 1.583805564978215em;
		margin-bottom: .287857499569283em;
	}
	h4 {
		font-size: 28.01011924799386px;
		margin-top: 1.561935513828041em;
		line-height: 1.87432261659365em;
		margin-bottom: .345845057728222em;
	}
	h5 {
		font-size: 23.668594908863454px;
		margin-top: 1.84844094752817em;
		line-height: 2.218129137033805em;
		margin-bottom: .369688189505634em;
	}
	h6 {
		font-size: 20px;
		margin-top: 2.1875em;
		line-height: 2.625em;
		margin-bottom: .473958333333333em;
	}
	fieldset {
		margin-bottom: 2.078125em;
	}
	input[type=email] {
		font-size: 20px;
		margin-bottom: .5140625em;
	}
	input[type=password] {
		font-size: 20px;
		margin-bottom: .5140625em;
	}
	input[type=text] {
		font-size: 20px;
		margin-bottom: .5140625em;
	}
	textarea {
		font-size: 20px;
		margin-bottom: .5140625em;
	}
	button {
		font-size: 20px;
		margin-bottom: 1.3125em;
	}
	input[type=submit] {
		font-size: 20px;
		margin-bottom: 1.3125em;
	}
	table {
		margin-bottom: 2.05625em;
	}
	th {
		padding: .4375em .875em;
	}
	td {
		padding: .4375em .875em;
	}
}
input[type=email] {
	display: block;
	max-width: 100%;
	padding: .4375em;
	font-size: 12px;
	margin-bottom: 1.18125em;
}
input[type=password] {
	display: block;
	max-width: 100%;
	padding: .4375em;
	font-size: 12px;
	margin-bottom: 1.18125em;
}
input[type=text] {
	display: block;
	max-width: 100%;
	padding: .4375em;
	font-size: 12px;
	margin-bottom: 1.18125em;
}
.master {
	background-image: url($url_1);
	background-size: cover;
	background-position: top;
	min-height: 100vh;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
}
.box {
	width: 450px;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 0 0 3px 3px;
	overflow: hidden;
	//Instead of the line below you could use @include box-sizing($bs)
	box-sizing: border-box;
	//Instead of the line below you could use @include box-shadow($shadow-1, $shadow-2, $shadow-3, $shadow-4, $shadow-5, $shadow-6, $shadow-7, $shadow-8, $shadow-9, $shadow-10)
	box-shadow: 0 10px 10px $color_7, 0 6px 3px $color_8;
}
.header {
	background-color: $color_9;
	padding: 30px 30px 40px;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 3px 3px 0 0;
	text-align: center;
}
.header__step {
	font-weight: 300;
	
	font-size: 14px;
	letter-spacing: 1.1px;
	margin: 0 0 10px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	//Instead of the line below you could use @include user-select($select)
	user-select: none;
	color: $color_10;
}
.header__title {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	//Instead of the line below you could use @include user-select($select)
	user-select: none;
	color: $color_10;
	font-weight: 400;
	font-size: 20px;
	margin: 0 0 15px;
}
.step {
	position: relative;
	z-index: 1;
	padding-left: 0;
	list-style: none;
	margin-bottom: 0;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: row-reverse;
	-ms-flex-direction: row-reverse;
	flex-direction: row-reverse;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
	margin-top: -20px;
}
.step__divider {
	background-color: $color_11;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	//Instead of the line below you could use @include user-select($select)
	user-select: none;
	width: 60px;
	height: 3px;
	&:first-child {
		-webkit-flex: 1 0 auto;
		-ms-flex: 1 0 auto;
		flex: 1 0 auto;
	}
	&:last-child {
		-webkit-flex: 1 0 auto;
		-ms-flex: 1 0 auto;
		flex: 1 0 auto;
	}
}
.step__icon {
	background-color: $color_11;
	font-style: normal;
	width: 40px;
	height: 40px;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 50%;
	color: $color_10;
	&.welcome:before {
		content: '\f144';
		font-family: $font_10;
	}
	&.requirements:before {
		content: '\f127';
		font-family: $font_10;
	}
	&.permissions:before {
		content: '\f296';
		font-family: $font_10;
	}
	&.database:before {
		content: '\f454';
		font-family: $font_10;
	}
	&.update:before {
		content: '\f2bf';
		font-family: $font_10;
	}
}
.main {
	margin-top: -20px;
	background-color: $color_10;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 0 0 3px 3px;
	padding: 40px 40px 30px;
}
.buttons {
	text-align: center;
	.button {
		margin: .5em;
	}
}
.buttons--right {
	text-align: right;
}
.button {
	display: inline-block;
	background-color: $color_12;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 2px;
	padding: 7px 20px;
	color: $color_10;
	//Instead of the line below you could use @include box-shadow($shadow-1, $shadow-2, $shadow-3, $shadow-4, $shadow-5, $shadow-6, $shadow-7, $shadow-8, $shadow-9, $shadow-10)
	box-shadow: 0 1px 1.5px $color_14, 0 1px 1px $color_15;
	text-decoration: none;
	outline: none;
	border: none;
	//Instead of the line below you could use @include transition($transition-1, $transition-2, $transition-3, $transition-4, $transition-5, $transition-6, $transition-7, $transition-8, $transition-9, $transition-10)
	transition: box-shadow .2s ease, background-color .2s ease;
	cursor: pointer;
	&:hover {
		color: $color_10;
		//Instead of the line below you could use @include box-shadow($shadow-1, $shadow-2, $shadow-3, $shadow-4, $shadow-5, $shadow-6, $shadow-7, $shadow-8, $shadow-9, $shadow-10)
		box-shadow: 0 10px 10px $color_7, 0 6px 3px $color_8;
		background-color: $color_16;
	}
}
.button--light {
	padding: 3px 16px;
	font-size: 16px;
	border-top: 1px solid $color_17;
	color: $color_18;
	background: $color_10;
	&:hover {
		color: $color_18;
		background: $color_10;
		//Instead of the line below you could use @include box-shadow($shadow-1, $shadow-2, $shadow-3, $shadow-4, $shadow-5, $shadow-6, $shadow-7, $shadow-8, $shadow-9, $shadow-10)
		box-shadow: 0 3px 3px $color_19, 0 3px 3px $color_8;
	}
}
.list {
	padding-left: 0;
	list-style: none;
	margin-bottom: 0;
	margin: 20px 0 35px;
	border: 1px solid $color_14;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 2px;
	.list__item.list__title {
		background: $color_14;
		&.success {
			span {
				color: $color_31;
			}
			.fa:before {
				color: $color_31;
			}
		}
		&.error {
			span {
				color: $color_24;
			}
			.fa:before {
				color: $color_24;
			}
		}
	}
}
.list__item {
	position: relative;
	overflow: hidden;
	padding: 7px 20px;
	border-bottom: 1px solid $color_14;
	&:first-letter {
		
	}
	&:last-child {
		border-bottom: none;
	}
	.fa.row-icon:before {
		display: -webkit-flex;
		display: -ms-flexbox;
		display: flex;
		-webkit-justify-content: center;
		-ms-flex-pack: center;
		justify-content: center;
		-webkit-align-items: center;
		-ms-flex-align: center;
		align-items: center;
		padding: 7px 20px;
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
	}
	&.success .fa:before {
		color: $color_20;
	}
	&.error .fa:before {
		color: $color_21;
	}
}
.list__item--permissions {
	&:before {
		content: '' !important;
	}
	span {
		display: -webkit-flex;
		display: -ms-flexbox;
		display: flex;
		-webkit-justify-content: center;
		-ms-flex-pack: center;
		justify-content: center;
		-webkit-align-items: center;
		-ms-flex-align: center;
		align-items: center;
		padding: 7px 20px;
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		background-color: $color_22;
		font-weight: 700;
		font-size: 16px;
		&:before {
			margin-right: 7px;
			font-weight: 400;
		}
	}
	&.success i:before {
		color: $color_20;
		vertical-align: 1px;
	}
	&.error i:before {
		color: $color_21;
		vertical-align: 1px;
	}
}
.textarea {
	//Instead of the line below you could use @include box-sizing($bs)
	box-sizing: border-box;
	width: 100%;
	font-size: 14px;
	line-height: 25px;
	height: 150px;
	outline: none;
	border: 1px solid $color_23;
	~ .button {
		margin-bottom: 35px;
	}
}
.text-center {
	text-align: center;
}
.form-control {
	height: 14px;
	width: 100%;
}
.has-error {
	color: $color_24;
	input {
		color: $color_25;
		border: 1px solid $color_26;
	}
}
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	border: 0;
}
input[type='text'] {
	outline: none;
	border: 1px solid $color_27;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 3px;
	padding: 10px 12px;
	width: calc(100% - 24px);
	margin: 0 auto 1em;
}
input[type='password'] {
	outline: none;
	border: 1px solid $color_27;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 3px;
	padding: 10px 12px;
	width: calc(100% - 24px);
	margin: 0 auto 1em;
}
input[type='url'] {
	outline: none;
	border: 1px solid $color_27;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 3px;
	padding: 10px 12px;
	width: calc(100% - 24px);
	margin: 0 auto 1em;
}
input[type='number'] {
	outline: none;
	border: 1px solid $color_27;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 3px;
	padding: 10px 12px;
	width: calc(100% - 24px);
	margin: 0 auto 1em;
}
.tabs {
	padding: 0;
	.tab-input {
		display: none;
		&:checked + .tab-label {
			background-color: $color_10;
			color: $color_29;
		}
	}
	.tab-label {
		color: $color_28;
		cursor: pointer;
		float: left;
		padding: 1em;
		text-align: center;
		-webkit-transition: all 0.1s ease-in-out;
		-moz-transition: all 0.1s ease-in-out;
		-o-transition: all 0.1s ease-in-out;
		//Instead of the line below you could use @include transition($transition-1, $transition-2, $transition-3, $transition-4, $transition-5, $transition-6, $transition-7, $transition-8, $transition-9, $transition-10)
		transition: all 0.1s ease-in-out;
		&:hover {
			color: $color_29;
		}
	}
	.tabs-wrap {
		clear: both;
	}
	.tab {
		display: none;
		> *:last-child {
			margin-bottom: 0;
		}
	}
}
.float-left {
	float: left;
}
.float-right {
	float: right;
}
.buttons-container {
	min-height: 37px;
	margin: 1em 0 0;
}
.block {
	//Instead of the line below you could use @include box-shadow($shadow-1, $shadow-2, $shadow-3, $shadow-4, $shadow-5, $shadow-6, $shadow-7, $shadow-8, $shadow-9, $shadow-10)
	box-shadow: 0 3px 1px $color_32;
	input[type='radio'] {
		width: 100%;
		display: none;
		+ label {
			background: $color_33;
			color: $color_10;
			padding-top: 5px;
			-webkit-transition: all 0.1s ease-in-out;
			-moz-transition: all 0.1s ease-in-out;
			-o-transition: all 0.1s ease-in-out;
			//Instead of the line below you could use @include transition($transition-1, $transition-2, $transition-3, $transition-4, $transition-5, $transition-6, $transition-7, $transition-8, $transition-9, $transition-10)
			transition: all 0.1s ease-in-out;
			&:hover {
				background: $color_34;
				color: $color_10;
				padding-top: 5px;
			}
		}
		&:checked {
			+ label {
				background-color: $color_35;
			}
			~ .info {
				height: 200px;
				overflow-y: auto;
				-webkit-transition: all 0.3s ease-in-out;
				-moz-transition: all 0.3s ease-in-out;
				-o-transition: all 0.3s ease-in-out;
				//Instead of the line below you could use @include transition($transition-1, $transition-2, $transition-3, $transition-4, $transition-5, $transition-6, $transition-7, $transition-8, $transition-9, $transition-10)
				transition: all 0.3s ease-in-out;
			}
		}
		~ .info > div {
			padding-top: 15px;
		}
	}
	label {
		width: 450px;
		max-width: 100%;
		cursor: pointer;
	}
	span {
		font-family: $font_5;
		font-weight: 700;
		display: block;
		padding: 10px 12px 12px 15px;
		margin: 0;
		cursor: pointer;
	}
}
.info {
	background: $color_10;
	color: $color_18;
	width: 100%;
	height: 0;
	line-height: 2;
	padding-left: 15px;
	padding-right: 15px;
	display: block;
	overflow: hidden;
	//Instead of the line below you could use @include box-sizing($bs)
	box-sizing: border-box;
	//Instead of the line below you could use @include transition($transition-1, $transition-2, $transition-3, $transition-4, $transition-5, $transition-6, $transition-7, $transition-8, $transition-9, $transition-10)
	transition: .3s ease-out;
}
::selection {
	background: $color_18;
	color: $color_10;
}
::-webkit-scrollbar {
	width: 12px;
}
::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 6px $color_36;
	-webkit-border-radius: 0;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 0;
}
::-webkit-scrollbar-thumb {
	-webkit-border-radius: 0;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 0;
	background: $color_37;
	-webkit-box-shadow: inset 0 0 6px $color_38;
}
.margin-bottom-1 {
	margin-bottom: 1em;
}
.margin-bottom-2 {
	margin-bottom: 1em;
}
.margin-top-1 {
	margin-top: 1em;
}
.margin-top-2 {
	margin-top: 1em;
}
.alert {
	margin: 0 0 10px;
	font-size: 1.1em;
	background-color: $color_22;
	//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
	border-radius: 3px;
	padding: 10px;
	position: relative;
	&.alert-danger {
		background: $color_24;
		color: $color_39;
		padding: 10px 20px 15px;
		h4 {
			color: $color_39;
			margin: 0;
		}
		ul {
			margin: 0;
		}
	}
	.close {
		width: 25px;
		height: 25px;
		padding: 0;
		margin: 0;
		//Instead of the line below you could use @include box-shadow($shadow-1, $shadow-2, $shadow-3, $shadow-4, $shadow-5, $shadow-6, $shadow-7, $shadow-8, $shadow-9, $shadow-10)
		box-shadow: none;
		border: 2px solid $color_26;
		outline: none;
		float: right;
		//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
		border-radius: 50%;
		position: absolute;
		right: 0;
		top: 0;
		background-color: transparent;
		cursor: pointer;
		-webkit-transition: all 0.1s ease-in-out;
		-moz-transition: all 0.1s ease-in-out;
		-o-transition: all 0.1s ease-in-out;
		//Instead of the line below you could use @include transition($transition-1, $transition-2, $transition-3, $transition-4, $transition-5, $transition-6, $transition-7, $transition-8, $transition-9, $transition-10)
		transition: all 0.1s ease-in-out;
		&:hover {
			background-color: $color_39;
			color: $color_24;
		}
	}
}
svg:not(:root) {
	overflow: hidden;
}
.step__item.active .step__icon,
.step__item.active~.step__divider,
.step__item.active~.step__item .step__icon {
    background-color: $color_12;
    -webkit-transition: all 0.1s ease-in-out;
    -moz-transition: all 0.1s ease-in-out;
    -o-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
}
.step__item.active .step__icon:hover,
.step__item.active~.step__item .step__icon:hover {
    background-color: $color_13;
}
.form-group.has-error {
	select {
		border-color: $color_24;
	}
	textarea {
		border-color: $color_24;
	}
	input[type='text'] {
		border-color: $color_24;
	}
	input[type='password'] {
		border-color: $color_24;
	}
	input[type='url'] {
		border-color: $color_24;
	}
	input[type='number'] {
		border-color: $color_24;
	}
	.error-block {
		margin: -12px 0 0;
		display: block;
		width: 100%;
		font-size: .9em;
		color: $color_24;
		font-weight: 500;
	}
}
.tabs-full .tab-label {
	display: table-cell;
	float: none;
	width: 1%;
}
#tab1:checked ~ .tabs-wrap #tab1content {
	display: block;
}
#tab2:checked ~ .tabs-wrap #tab2content {
	display: block;
}
#tab3:checked ~ .tabs-wrap #tab3content {
	display: block;
}
#tab4:checked ~ .tabs-wrap #tab4content {
	display: block;
}
#tab5:checked ~ .tabs-wrap #tab5content {
	display: block;
}
.github img {
	position: absolute;
	top: 0;
	right: 0;
	border: 0;
}
#tab3content .block {
	&:first-child {
		//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
		border-radius: 3px 3px 0 0;
	}
	&:last-child {
		//Instead of the line below you could use @include border-radius($radius, $vertical-radius)
		border-radius: 0 0 3px 3px;
	}
}