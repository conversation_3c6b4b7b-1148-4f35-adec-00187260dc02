@charset "UTF-8";
/*!
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
/* FONT PATH
 * -------------------------- */
@import url("https://fonts.googleapis.com/css?family=Roboto:400,300,500,700,900");
@font-face {
  font-family: 'FontAwesome';
  src: url("../fonts/fontawesome-webfont.eot?v=4.7.0");
  src: url("../fonts/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("../fonts/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("../fonts/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("../fonts/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("../fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal; }
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

/* makes the font 33% larger relative to the icon container */
.fa-lg {
  font-size: 1.3333333333em;
  line-height: 0.75em;
  vertical-align: -15%; }

.fa-2x {
  font-size: 2em; }

.fa-3x {
  font-size: 3em; }

.fa-4x {
  font-size: 4em; }

.fa-5x {
  font-size: 5em; }

.fa-fw {
  width: 1.2857142857em;
  text-align: center; }

.fa-ul {
  padding-left: 0;
  margin-left: 2.1428571429em;
  list-style-type: none; }
  .fa-ul > li {
    position: relative; }

.fa-li {
  position: absolute;
  left: -2.1428571429em;
  width: 2.1428571429em;
  top: 0.1428571429em;
  text-align: center; }
  .fa-li.fa-lg {
    left: -1.8571428571em; }

.fa-border {
  padding: .2em .25em .15em;
  border: solid 0.08em #eee;
  border-radius: .1em; }

.fa-pull-left {
  float: left; }

.fa-pull-right {
  float: right; }

.fa.fa-pull-left {
  margin-right: .3em; }
.fa.fa-pull-right {
  margin-left: .3em; }

/* Deprecated as of 4.4.0 */
.pull-right {
  float: right; }

.pull-left {
  float: left; }

.fa.pull-left {
  margin-right: .3em; }
.fa.pull-right {
  margin-left: .3em; }

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear; }

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8); }

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg); } }
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg); } }
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg); }

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg); }

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg); }

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  transform: scale(-1, 1); }

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  transform: scale(1, -1); }

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
  -webkit-filter: none;
          filter: none; }

.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle; }

.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center; }

.fa-stack-1x {
  line-height: inherit; }

.fa-stack-2x {
  font-size: 2em; }

.fa-inverse {
  color: #fff; }

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.fa-glass:before {
  content: ""; }

.fa-music:before {
  content: ""; }

.fa-search:before {
  content: ""; }

.fa-envelope-o:before {
  content: ""; }

.fa-heart:before {
  content: ""; }

.fa-star:before {
  content: ""; }

.fa-star-o:before {
  content: ""; }

.fa-user:before {
  content: ""; }

.fa-film:before {
  content: ""; }

.fa-th-large:before {
  content: ""; }

.fa-th:before {
  content: ""; }

.fa-th-list:before {
  content: ""; }

.fa-check:before {
  content: ""; }

.fa-remove:before,
.fa-close:before,
.fa-times:before {
  content: ""; }

.fa-search-plus:before {
  content: ""; }

.fa-search-minus:before {
  content: ""; }

.fa-power-off:before {
  content: ""; }

.fa-signal:before {
  content: ""; }

.fa-gear:before,
.fa-cog:before {
  content: ""; }

.fa-trash-o:before {
  content: ""; }

.fa-home:before {
  content: ""; }

.fa-file-o:before {
  content: ""; }

.fa-clock-o:before {
  content: ""; }

.fa-road:before {
  content: ""; }

.fa-download:before {
  content: ""; }

.fa-arrow-circle-o-down:before {
  content: ""; }

.fa-arrow-circle-o-up:before {
  content: ""; }

.fa-inbox:before {
  content: ""; }

.fa-play-circle-o:before {
  content: ""; }

.fa-rotate-right:before,
.fa-repeat:before {
  content: ""; }

.fa-refresh:before {
  content: ""; }

.fa-list-alt:before {
  content: ""; }

.fa-lock:before {
  content: ""; }

.fa-flag:before {
  content: ""; }

.fa-headphones:before {
  content: ""; }

.fa-volume-off:before {
  content: ""; }

.fa-volume-down:before {
  content: ""; }

.fa-volume-up:before {
  content: ""; }

.fa-qrcode:before {
  content: ""; }

.fa-barcode:before {
  content: ""; }

.fa-tag:before {
  content: ""; }

.fa-tags:before {
  content: ""; }

.fa-book:before {
  content: ""; }

.fa-bookmark:before {
  content: ""; }

.fa-print:before {
  content: ""; }

.fa-camera:before {
  content: ""; }

.fa-font:before {
  content: ""; }

.fa-bold:before {
  content: ""; }

.fa-italic:before {
  content: ""; }

.fa-text-height:before {
  content: ""; }

.fa-text-width:before {
  content: ""; }

.fa-align-left:before {
  content: ""; }

.fa-align-center:before {
  content: ""; }

.fa-align-right:before {
  content: ""; }

.fa-align-justify:before {
  content: ""; }

.fa-list:before {
  content: ""; }

.fa-dedent:before,
.fa-outdent:before {
  content: ""; }

.fa-indent:before {
  content: ""; }

.fa-video-camera:before {
  content: ""; }

.fa-photo:before,
.fa-image:before,
.fa-picture-o:before {
  content: ""; }

.fa-pencil:before {
  content: ""; }

.fa-map-marker:before {
  content: ""; }

.fa-adjust:before {
  content: ""; }

.fa-tint:before {
  content: ""; }

.fa-edit:before,
.fa-pencil-square-o:before {
  content: ""; }

.fa-share-square-o:before {
  content: ""; }

.fa-check-square-o:before {
  content: ""; }

.fa-arrows:before {
  content: ""; }

.fa-step-backward:before {
  content: ""; }

.fa-fast-backward:before {
  content: ""; }

.fa-backward:before {
  content: ""; }

.fa-play:before {
  content: ""; }

.fa-pause:before {
  content: ""; }

.fa-stop:before {
  content: ""; }

.fa-forward:before {
  content: ""; }

.fa-fast-forward:before {
  content: ""; }

.fa-step-forward:before {
  content: ""; }

.fa-eject:before {
  content: ""; }

.fa-chevron-left:before {
  content: ""; }

.fa-chevron-right:before {
  content: ""; }

.fa-plus-circle:before {
  content: ""; }

.fa-minus-circle:before {
  content: ""; }

.fa-times-circle:before {
  content: ""; }

.fa-check-circle:before {
  content: ""; }

.fa-question-circle:before {
  content: ""; }

.fa-info-circle:before {
  content: ""; }

.fa-crosshairs:before {
  content: ""; }

.fa-times-circle-o:before {
  content: ""; }

.fa-check-circle-o:before {
  content: ""; }

.fa-ban:before {
  content: ""; }

.fa-arrow-left:before {
  content: ""; }

.fa-arrow-right:before {
  content: ""; }

.fa-arrow-up:before {
  content: ""; }

.fa-arrow-down:before {
  content: ""; }

.fa-mail-forward:before,
.fa-share:before {
  content: ""; }

.fa-expand:before {
  content: ""; }

.fa-compress:before {
  content: ""; }

.fa-plus:before {
  content: ""; }

.fa-minus:before {
  content: ""; }

.fa-asterisk:before {
  content: ""; }

.fa-exclamation-circle:before {
  content: ""; }

.fa-gift:before {
  content: ""; }

.fa-leaf:before {
  content: ""; }

.fa-fire:before {
  content: ""; }

.fa-eye:before {
  content: ""; }

.fa-eye-slash:before {
  content: ""; }

.fa-warning:before,
.fa-exclamation-triangle:before {
  content: ""; }

.fa-plane:before {
  content: ""; }

.fa-calendar:before {
  content: ""; }

.fa-random:before {
  content: ""; }

.fa-comment:before {
  content: ""; }

.fa-magnet:before {
  content: ""; }

.fa-chevron-up:before {
  content: ""; }

.fa-chevron-down:before {
  content: ""; }

.fa-retweet:before {
  content: ""; }

.fa-shopping-cart:before {
  content: ""; }

.fa-folder:before {
  content: ""; }

.fa-folder-open:before {
  content: ""; }

.fa-arrows-v:before {
  content: ""; }

.fa-arrows-h:before {
  content: ""; }

.fa-bar-chart-o:before,
.fa-bar-chart:before {
  content: ""; }

.fa-twitter-square:before {
  content: ""; }

.fa-facebook-square:before {
  content: ""; }

.fa-camera-retro:before {
  content: ""; }

.fa-key:before {
  content: ""; }

.fa-gears:before,
.fa-cogs:before {
  content: ""; }

.fa-comments:before {
  content: ""; }

.fa-thumbs-o-up:before {
  content: ""; }

.fa-thumbs-o-down:before {
  content: ""; }

.fa-star-half:before {
  content: ""; }

.fa-heart-o:before {
  content: ""; }

.fa-sign-out:before {
  content: ""; }

.fa-linkedin-square:before {
  content: ""; }

.fa-thumb-tack:before {
  content: ""; }

.fa-external-link:before {
  content: ""; }

.fa-sign-in:before {
  content: ""; }

.fa-trophy:before {
  content: ""; }

.fa-github-square:before {
  content: ""; }

.fa-upload:before {
  content: ""; }

.fa-lemon-o:before {
  content: ""; }

.fa-phone:before {
  content: ""; }

.fa-square-o:before {
  content: ""; }

.fa-bookmark-o:before {
  content: ""; }

.fa-phone-square:before {
  content: ""; }

.fa-twitter:before {
  content: ""; }

.fa-facebook-f:before,
.fa-facebook:before {
  content: ""; }

.fa-github:before {
  content: ""; }

.fa-unlock:before {
  content: ""; }

.fa-credit-card:before {
  content: ""; }

.fa-feed:before,
.fa-rss:before {
  content: ""; }

.fa-hdd-o:before {
  content: ""; }

.fa-bullhorn:before {
  content: ""; }

.fa-bell:before {
  content: ""; }

.fa-certificate:before {
  content: ""; }

.fa-hand-o-right:before {
  content: ""; }

.fa-hand-o-left:before {
  content: ""; }

.fa-hand-o-up:before {
  content: ""; }

.fa-hand-o-down:before {
  content: ""; }

.fa-arrow-circle-left:before {
  content: ""; }

.fa-arrow-circle-right:before {
  content: ""; }

.fa-arrow-circle-up:before {
  content: ""; }

.fa-arrow-circle-down:before {
  content: ""; }

.fa-globe:before {
  content: ""; }

.fa-wrench:before {
  content: ""; }

.fa-tasks:before {
  content: ""; }

.fa-filter:before {
  content: ""; }

.fa-briefcase:before {
  content: ""; }

.fa-arrows-alt:before {
  content: ""; }

.fa-group:before,
.fa-users:before {
  content: ""; }

.fa-chain:before,
.fa-link:before {
  content: ""; }

.fa-cloud:before {
  content: ""; }

.fa-flask:before {
  content: ""; }

.fa-cut:before,
.fa-scissors:before {
  content: ""; }

.fa-copy:before,
.fa-files-o:before {
  content: ""; }

.fa-paperclip:before {
  content: ""; }

.fa-save:before,
.fa-floppy-o:before {
  content: ""; }

.fa-square:before {
  content: ""; }

.fa-navicon:before,
.fa-reorder:before,
.fa-bars:before {
  content: ""; }

.fa-list-ul:before {
  content: ""; }

.fa-list-ol:before {
  content: ""; }

.fa-strikethrough:before {
  content: ""; }

.fa-underline:before {
  content: ""; }

.fa-table:before {
  content: ""; }

.fa-magic:before {
  content: ""; }

.fa-truck:before {
  content: ""; }

.fa-pinterest:before {
  content: ""; }

.fa-pinterest-square:before {
  content: ""; }

.fa-google-plus-square:before {
  content: ""; }

.fa-google-plus:before {
  content: ""; }

.fa-money:before {
  content: ""; }

.fa-caret-down:before {
  content: ""; }

.fa-caret-up:before {
  content: ""; }

.fa-caret-left:before {
  content: ""; }

.fa-caret-right:before {
  content: ""; }

.fa-columns:before {
  content: ""; }

.fa-unsorted:before,
.fa-sort:before {
  content: ""; }

.fa-sort-down:before,
.fa-sort-desc:before {
  content: ""; }

.fa-sort-up:before,
.fa-sort-asc:before {
  content: ""; }

.fa-envelope:before {
  content: ""; }

.fa-linkedin:before {
  content: ""; }

.fa-rotate-left:before,
.fa-undo:before {
  content: ""; }

.fa-legal:before,
.fa-gavel:before {
  content: ""; }

.fa-dashboard:before,
.fa-tachometer:before {
  content: ""; }

.fa-comment-o:before {
  content: ""; }

.fa-comments-o:before {
  content: ""; }

.fa-flash:before,
.fa-bolt:before {
  content: ""; }

.fa-sitemap:before {
  content: ""; }

.fa-umbrella:before {
  content: ""; }

.fa-paste:before,
.fa-clipboard:before {
  content: ""; }

.fa-lightbulb-o:before {
  content: ""; }

.fa-exchange:before {
  content: ""; }

.fa-cloud-download:before {
  content: ""; }

.fa-cloud-upload:before {
  content: ""; }

.fa-user-md:before {
  content: ""; }

.fa-stethoscope:before {
  content: ""; }

.fa-suitcase:before {
  content: ""; }

.fa-bell-o:before {
  content: ""; }

.fa-coffee:before {
  content: ""; }

.fa-cutlery:before {
  content: ""; }

.fa-file-text-o:before {
  content: ""; }

.fa-building-o:before {
  content: ""; }

.fa-hospital-o:before {
  content: ""; }

.fa-ambulance:before {
  content: ""; }

.fa-medkit:before {
  content: ""; }

.fa-fighter-jet:before {
  content: ""; }

.fa-beer:before {
  content: ""; }

.fa-h-square:before {
  content: ""; }

.fa-plus-square:before {
  content: ""; }

.fa-angle-double-left:before {
  content: ""; }

.fa-angle-double-right:before {
  content: ""; }

.fa-angle-double-up:before {
  content: ""; }

.fa-angle-double-down:before {
  content: ""; }

.fa-angle-left:before {
  content: ""; }

.fa-angle-right:before {
  content: ""; }

.fa-angle-up:before {
  content: ""; }

.fa-angle-down:before {
  content: ""; }

.fa-desktop:before {
  content: ""; }

.fa-laptop:before {
  content: ""; }

.fa-tablet:before {
  content: ""; }

.fa-mobile-phone:before,
.fa-mobile:before {
  content: ""; }

.fa-circle-o:before {
  content: ""; }

.fa-quote-left:before {
  content: ""; }

.fa-quote-right:before {
  content: ""; }

.fa-spinner:before {
  content: ""; }

.fa-circle:before {
  content: ""; }

.fa-mail-reply:before,
.fa-reply:before {
  content: ""; }

.fa-github-alt:before {
  content: ""; }

.fa-folder-o:before {
  content: ""; }

.fa-folder-open-o:before {
  content: ""; }

.fa-smile-o:before {
  content: ""; }

.fa-frown-o:before {
  content: ""; }

.fa-meh-o:before {
  content: ""; }

.fa-gamepad:before {
  content: ""; }

.fa-keyboard-o:before {
  content: ""; }

.fa-flag-o:before {
  content: ""; }

.fa-flag-checkered:before {
  content: ""; }

.fa-terminal:before {
  content: ""; }

.fa-code:before {
  content: ""; }

.fa-mail-reply-all:before,
.fa-reply-all:before {
  content: ""; }

.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
  content: ""; }

.fa-location-arrow:before {
  content: ""; }

.fa-crop:before {
  content: ""; }

.fa-code-fork:before {
  content: ""; }

.fa-unlink:before,
.fa-chain-broken:before {
  content: ""; }

.fa-question:before {
  content: ""; }

.fa-info:before {
  content: ""; }

.fa-exclamation:before {
  content: ""; }

.fa-superscript:before {
  content: ""; }

.fa-subscript:before {
  content: ""; }

.fa-eraser:before {
  content: ""; }

.fa-puzzle-piece:before {
  content: ""; }

.fa-microphone:before {
  content: ""; }

.fa-microphone-slash:before {
  content: ""; }

.fa-shield:before {
  content: ""; }

.fa-calendar-o:before {
  content: ""; }

.fa-fire-extinguisher:before {
  content: ""; }

.fa-rocket:before {
  content: ""; }

.fa-maxcdn:before {
  content: ""; }

.fa-chevron-circle-left:before {
  content: ""; }

.fa-chevron-circle-right:before {
  content: ""; }

.fa-chevron-circle-up:before {
  content: ""; }

.fa-chevron-circle-down:before {
  content: ""; }

.fa-html5:before {
  content: ""; }

.fa-css3:before {
  content: ""; }

.fa-anchor:before {
  content: ""; }

.fa-unlock-alt:before {
  content: ""; }

.fa-bullseye:before {
  content: ""; }

.fa-ellipsis-h:before {
  content: ""; }

.fa-ellipsis-v:before {
  content: ""; }

.fa-rss-square:before {
  content: ""; }

.fa-play-circle:before {
  content: ""; }

.fa-ticket:before {
  content: ""; }

.fa-minus-square:before {
  content: ""; }

.fa-minus-square-o:before {
  content: ""; }

.fa-level-up:before {
  content: ""; }

.fa-level-down:before {
  content: ""; }

.fa-check-square:before {
  content: ""; }

.fa-pencil-square:before {
  content: ""; }

.fa-external-link-square:before {
  content: ""; }

.fa-share-square:before {
  content: ""; }

.fa-compass:before {
  content: ""; }

.fa-toggle-down:before,
.fa-caret-square-o-down:before {
  content: ""; }

.fa-toggle-up:before,
.fa-caret-square-o-up:before {
  content: ""; }

.fa-toggle-right:before,
.fa-caret-square-o-right:before {
  content: ""; }

.fa-euro:before,
.fa-eur:before {
  content: ""; }

.fa-gbp:before {
  content: ""; }

.fa-dollar:before,
.fa-usd:before {
  content: ""; }

.fa-rupee:before,
.fa-inr:before {
  content: ""; }

.fa-cny:before,
.fa-rmb:before,
.fa-yen:before,
.fa-jpy:before {
  content: ""; }

.fa-ruble:before,
.fa-rouble:before,
.fa-rub:before {
  content: ""; }

.fa-won:before,
.fa-krw:before {
  content: ""; }

.fa-bitcoin:before,
.fa-btc:before {
  content: ""; }

.fa-file:before {
  content: ""; }

.fa-file-text:before {
  content: ""; }

.fa-sort-alpha-asc:before {
  content: ""; }

.fa-sort-alpha-desc:before {
  content: ""; }

.fa-sort-amount-asc:before {
  content: ""; }

.fa-sort-amount-desc:before {
  content: ""; }

.fa-sort-numeric-asc:before {
  content: ""; }

.fa-sort-numeric-desc:before {
  content: ""; }

.fa-thumbs-up:before {
  content: ""; }

.fa-thumbs-down:before {
  content: ""; }

.fa-youtube-square:before {
  content: ""; }

.fa-youtube:before {
  content: ""; }

.fa-xing:before {
  content: ""; }

.fa-xing-square:before {
  content: ""; }

.fa-youtube-play:before {
  content: ""; }

.fa-dropbox:before {
  content: ""; }

.fa-stack-overflow:before {
  content: ""; }

.fa-instagram:before {
  content: ""; }

.fa-flickr:before {
  content: ""; }

.fa-adn:before {
  content: ""; }

.fa-bitbucket:before {
  content: ""; }

.fa-bitbucket-square:before {
  content: ""; }

.fa-tumblr:before {
  content: ""; }

.fa-tumblr-square:before {
  content: ""; }

.fa-long-arrow-down:before {
  content: ""; }

.fa-long-arrow-up:before {
  content: ""; }

.fa-long-arrow-left:before {
  content: ""; }

.fa-long-arrow-right:before {
  content: ""; }

.fa-apple:before {
  content: ""; }

.fa-windows:before {
  content: ""; }

.fa-android:before {
  content: ""; }

.fa-linux:before {
  content: ""; }

.fa-dribbble:before {
  content: ""; }

.fa-skype:before {
  content: ""; }

.fa-foursquare:before {
  content: ""; }

.fa-trello:before {
  content: ""; }

.fa-female:before {
  content: ""; }

.fa-male:before {
  content: ""; }

.fa-gittip:before,
.fa-gratipay:before {
  content: ""; }

.fa-sun-o:before {
  content: ""; }

.fa-moon-o:before {
  content: ""; }

.fa-archive:before {
  content: ""; }

.fa-bug:before {
  content: ""; }

.fa-vk:before {
  content: ""; }

.fa-weibo:before {
  content: ""; }

.fa-renren:before {
  content: ""; }

.fa-pagelines:before {
  content: ""; }

.fa-stack-exchange:before {
  content: ""; }

.fa-arrow-circle-o-right:before {
  content: ""; }

.fa-arrow-circle-o-left:before {
  content: ""; }

.fa-toggle-left:before,
.fa-caret-square-o-left:before {
  content: ""; }

.fa-dot-circle-o:before {
  content: ""; }

.fa-wheelchair:before {
  content: ""; }

.fa-vimeo-square:before {
  content: ""; }

.fa-turkish-lira:before,
.fa-try:before {
  content: ""; }

.fa-plus-square-o:before {
  content: ""; }

.fa-space-shuttle:before {
  content: ""; }

.fa-slack:before {
  content: ""; }

.fa-envelope-square:before {
  content: ""; }

.fa-wordpress:before {
  content: ""; }

.fa-openid:before {
  content: ""; }

.fa-institution:before,
.fa-bank:before,
.fa-university:before {
  content: ""; }

.fa-mortar-board:before,
.fa-graduation-cap:before {
  content: ""; }

.fa-yahoo:before {
  content: ""; }

.fa-google:before {
  content: ""; }

.fa-reddit:before {
  content: ""; }

.fa-reddit-square:before {
  content: ""; }

.fa-stumbleupon-circle:before {
  content: ""; }

.fa-stumbleupon:before {
  content: ""; }

.fa-delicious:before {
  content: ""; }

.fa-digg:before {
  content: ""; }

.fa-pied-piper-pp:before {
  content: ""; }

.fa-pied-piper-alt:before {
  content: ""; }

.fa-drupal:before {
  content: ""; }

.fa-joomla:before {
  content: ""; }

.fa-language:before {
  content: ""; }

.fa-fax:before {
  content: ""; }

.fa-building:before {
  content: ""; }

.fa-child:before {
  content: ""; }

.fa-paw:before {
  content: ""; }

.fa-spoon:before {
  content: ""; }

.fa-cube:before {
  content: ""; }

.fa-cubes:before {
  content: ""; }

.fa-behance:before {
  content: ""; }

.fa-behance-square:before {
  content: ""; }

.fa-steam:before {
  content: ""; }

.fa-steam-square:before {
  content: ""; }

.fa-recycle:before {
  content: ""; }

.fa-automobile:before,
.fa-car:before {
  content: ""; }

.fa-cab:before,
.fa-taxi:before {
  content: ""; }

.fa-tree:before {
  content: ""; }

.fa-spotify:before {
  content: ""; }

.fa-deviantart:before {
  content: ""; }

.fa-soundcloud:before {
  content: ""; }

.fa-database:before {
  content: ""; }

.fa-file-pdf-o:before {
  content: ""; }

.fa-file-word-o:before {
  content: ""; }

.fa-file-excel-o:before {
  content: ""; }

.fa-file-powerpoint-o:before {
  content: ""; }

.fa-file-photo-o:before,
.fa-file-picture-o:before,
.fa-file-image-o:before {
  content: ""; }

.fa-file-zip-o:before,
.fa-file-archive-o:before {
  content: ""; }

.fa-file-sound-o:before,
.fa-file-audio-o:before {
  content: ""; }

.fa-file-movie-o:before,
.fa-file-video-o:before {
  content: ""; }

.fa-file-code-o:before {
  content: ""; }

.fa-vine:before {
  content: ""; }

.fa-codepen:before {
  content: ""; }

.fa-jsfiddle:before {
  content: ""; }

.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-saver:before,
.fa-support:before,
.fa-life-ring:before {
  content: ""; }

.fa-circle-o-notch:before {
  content: ""; }

.fa-ra:before,
.fa-resistance:before,
.fa-rebel:before {
  content: ""; }

.fa-ge:before,
.fa-empire:before {
  content: ""; }

.fa-git-square:before {
  content: ""; }

.fa-git:before {
  content: ""; }

.fa-y-combinator-square:before,
.fa-yc-square:before,
.fa-hacker-news:before {
  content: ""; }

.fa-tencent-weibo:before {
  content: ""; }

.fa-qq:before {
  content: ""; }

.fa-wechat:before,
.fa-weixin:before {
  content: ""; }

.fa-send:before,
.fa-paper-plane:before {
  content: ""; }

.fa-send-o:before,
.fa-paper-plane-o:before {
  content: ""; }

.fa-history:before {
  content: ""; }

.fa-circle-thin:before {
  content: ""; }

.fa-header:before {
  content: ""; }

.fa-paragraph:before {
  content: ""; }

.fa-sliders:before {
  content: ""; }

.fa-share-alt:before {
  content: ""; }

.fa-share-alt-square:before {
  content: ""; }

.fa-bomb:before {
  content: ""; }

.fa-soccer-ball-o:before,
.fa-futbol-o:before {
  content: ""; }

.fa-tty:before {
  content: ""; }

.fa-binoculars:before {
  content: ""; }

.fa-plug:before {
  content: ""; }

.fa-slideshare:before {
  content: ""; }

.fa-twitch:before {
  content: ""; }

.fa-yelp:before {
  content: ""; }

.fa-newspaper-o:before {
  content: ""; }

.fa-wifi:before {
  content: ""; }

.fa-calculator:before {
  content: ""; }

.fa-paypal:before {
  content: ""; }

.fa-google-wallet:before {
  content: ""; }

.fa-cc-visa:before {
  content: ""; }

.fa-cc-mastercard:before {
  content: ""; }

.fa-cc-discover:before {
  content: ""; }

.fa-cc-amex:before {
  content: ""; }

.fa-cc-paypal:before {
  content: ""; }

.fa-cc-stripe:before {
  content: ""; }

.fa-bell-slash:before {
  content: ""; }

.fa-bell-slash-o:before {
  content: ""; }

.fa-trash:before {
  content: ""; }

.fa-copyright:before {
  content: ""; }

.fa-at:before {
  content: ""; }

.fa-eyedropper:before {
  content: ""; }

.fa-paint-brush:before {
  content: ""; }

.fa-birthday-cake:before {
  content: ""; }

.fa-area-chart:before {
  content: ""; }

.fa-pie-chart:before {
  content: ""; }

.fa-line-chart:before {
  content: ""; }

.fa-lastfm:before {
  content: ""; }

.fa-lastfm-square:before {
  content: ""; }

.fa-toggle-off:before {
  content: ""; }

.fa-toggle-on:before {
  content: ""; }

.fa-bicycle:before {
  content: ""; }

.fa-bus:before {
  content: ""; }

.fa-ioxhost:before {
  content: ""; }

.fa-angellist:before {
  content: ""; }

.fa-cc:before {
  content: ""; }

.fa-shekel:before,
.fa-sheqel:before,
.fa-ils:before {
  content: ""; }

.fa-meanpath:before {
  content: ""; }

.fa-buysellads:before {
  content: ""; }

.fa-connectdevelop:before {
  content: ""; }

.fa-dashcube:before {
  content: ""; }

.fa-forumbee:before {
  content: ""; }

.fa-leanpub:before {
  content: ""; }

.fa-sellsy:before {
  content: ""; }

.fa-shirtsinbulk:before {
  content: ""; }

.fa-simplybuilt:before {
  content: ""; }

.fa-skyatlas:before {
  content: ""; }

.fa-cart-plus:before {
  content: ""; }

.fa-cart-arrow-down:before {
  content: ""; }

.fa-diamond:before {
  content: ""; }

.fa-ship:before {
  content: ""; }

.fa-user-secret:before {
  content: ""; }

.fa-motorcycle:before {
  content: ""; }

.fa-street-view:before {
  content: ""; }

.fa-heartbeat:before {
  content: ""; }

.fa-venus:before {
  content: ""; }

.fa-mars:before {
  content: ""; }

.fa-mercury:before {
  content: ""; }

.fa-intersex:before,
.fa-transgender:before {
  content: ""; }

.fa-transgender-alt:before {
  content: ""; }

.fa-venus-double:before {
  content: ""; }

.fa-mars-double:before {
  content: ""; }

.fa-venus-mars:before {
  content: ""; }

.fa-mars-stroke:before {
  content: ""; }

.fa-mars-stroke-v:before {
  content: ""; }

.fa-mars-stroke-h:before {
  content: ""; }

.fa-neuter:before {
  content: ""; }

.fa-genderless:before {
  content: ""; }

.fa-facebook-official:before {
  content: ""; }

.fa-pinterest-p:before {
  content: ""; }

.fa-whatsapp:before {
  content: ""; }

.fa-server:before {
  content: ""; }

.fa-user-plus:before {
  content: ""; }

.fa-user-times:before {
  content: ""; }

.fa-hotel:before,
.fa-bed:before {
  content: ""; }

.fa-viacoin:before {
  content: ""; }

.fa-train:before {
  content: ""; }

.fa-subway:before {
  content: ""; }

.fa-medium:before {
  content: ""; }

.fa-yc:before,
.fa-y-combinator:before {
  content: ""; }

.fa-optin-monster:before {
  content: ""; }

.fa-opencart:before {
  content: ""; }

.fa-expeditedssl:before {
  content: ""; }

.fa-battery-4:before,
.fa-battery:before,
.fa-battery-full:before {
  content: ""; }

.fa-battery-3:before,
.fa-battery-three-quarters:before {
  content: ""; }

.fa-battery-2:before,
.fa-battery-half:before {
  content: ""; }

.fa-battery-1:before,
.fa-battery-quarter:before {
  content: ""; }

.fa-battery-0:before,
.fa-battery-empty:before {
  content: ""; }

.fa-mouse-pointer:before {
  content: ""; }

.fa-i-cursor:before {
  content: ""; }

.fa-object-group:before {
  content: ""; }

.fa-object-ungroup:before {
  content: ""; }

.fa-sticky-note:before {
  content: ""; }

.fa-sticky-note-o:before {
  content: ""; }

.fa-cc-jcb:before {
  content: ""; }

.fa-cc-diners-club:before {
  content: ""; }

.fa-clone:before {
  content: ""; }

.fa-balance-scale:before {
  content: ""; }

.fa-hourglass-o:before {
  content: ""; }

.fa-hourglass-1:before,
.fa-hourglass-start:before {
  content: ""; }

.fa-hourglass-2:before,
.fa-hourglass-half:before {
  content: ""; }

.fa-hourglass-3:before,
.fa-hourglass-end:before {
  content: ""; }

.fa-hourglass:before {
  content: ""; }

.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
  content: ""; }

.fa-hand-stop-o:before,
.fa-hand-paper-o:before {
  content: ""; }

.fa-hand-scissors-o:before {
  content: ""; }

.fa-hand-lizard-o:before {
  content: ""; }

.fa-hand-spock-o:before {
  content: ""; }

.fa-hand-pointer-o:before {
  content: ""; }

.fa-hand-peace-o:before {
  content: ""; }

.fa-trademark:before {
  content: ""; }

.fa-registered:before {
  content: ""; }

.fa-creative-commons:before {
  content: ""; }

.fa-gg:before {
  content: ""; }

.fa-gg-circle:before {
  content: ""; }

.fa-tripadvisor:before {
  content: ""; }

.fa-odnoklassniki:before {
  content: ""; }

.fa-odnoklassniki-square:before {
  content: ""; }

.fa-get-pocket:before {
  content: ""; }

.fa-wikipedia-w:before {
  content: ""; }

.fa-safari:before {
  content: ""; }

.fa-chrome:before {
  content: ""; }

.fa-firefox:before {
  content: ""; }

.fa-opera:before {
  content: ""; }

.fa-internet-explorer:before {
  content: ""; }

.fa-tv:before,
.fa-television:before {
  content: ""; }

.fa-contao:before {
  content: ""; }

.fa-500px:before {
  content: ""; }

.fa-amazon:before {
  content: ""; }

.fa-calendar-plus-o:before {
  content: ""; }

.fa-calendar-minus-o:before {
  content: ""; }

.fa-calendar-times-o:before {
  content: ""; }

.fa-calendar-check-o:before {
  content: ""; }

.fa-industry:before {
  content: ""; }

.fa-map-pin:before {
  content: ""; }

.fa-map-signs:before {
  content: ""; }

.fa-map-o:before {
  content: ""; }

.fa-map:before {
  content: ""; }

.fa-commenting:before {
  content: ""; }

.fa-commenting-o:before {
  content: ""; }

.fa-houzz:before {
  content: ""; }

.fa-vimeo:before {
  content: ""; }

.fa-black-tie:before {
  content: ""; }

.fa-fonticons:before {
  content: ""; }

.fa-reddit-alien:before {
  content: ""; }

.fa-edge:before {
  content: ""; }

.fa-credit-card-alt:before {
  content: ""; }

.fa-codiepie:before {
  content: ""; }

.fa-modx:before {
  content: ""; }

.fa-fort-awesome:before {
  content: ""; }

.fa-usb:before {
  content: ""; }

.fa-product-hunt:before {
  content: ""; }

.fa-mixcloud:before {
  content: ""; }

.fa-scribd:before {
  content: ""; }

.fa-pause-circle:before {
  content: ""; }

.fa-pause-circle-o:before {
  content: ""; }

.fa-stop-circle:before {
  content: ""; }

.fa-stop-circle-o:before {
  content: ""; }

.fa-shopping-bag:before {
  content: ""; }

.fa-shopping-basket:before {
  content: ""; }

.fa-hashtag:before {
  content: ""; }

.fa-bluetooth:before {
  content: ""; }

.fa-bluetooth-b:before {
  content: ""; }

.fa-percent:before {
  content: ""; }

.fa-gitlab:before {
  content: ""; }

.fa-wpbeginner:before {
  content: ""; }

.fa-wpforms:before {
  content: ""; }

.fa-envira:before {
  content: ""; }

.fa-universal-access:before {
  content: ""; }

.fa-wheelchair-alt:before {
  content: ""; }

.fa-question-circle-o:before {
  content: ""; }

.fa-blind:before {
  content: ""; }

.fa-audio-description:before {
  content: ""; }

.fa-volume-control-phone:before {
  content: ""; }

.fa-braille:before {
  content: ""; }

.fa-assistive-listening-systems:before {
  content: ""; }

.fa-asl-interpreting:before,
.fa-american-sign-language-interpreting:before {
  content: ""; }

.fa-deafness:before,
.fa-hard-of-hearing:before,
.fa-deaf:before {
  content: ""; }

.fa-glide:before {
  content: ""; }

.fa-glide-g:before {
  content: ""; }

.fa-signing:before,
.fa-sign-language:before {
  content: ""; }

.fa-low-vision:before {
  content: ""; }

.fa-viadeo:before {
  content: ""; }

.fa-viadeo-square:before {
  content: ""; }

.fa-snapchat:before {
  content: ""; }

.fa-snapchat-ghost:before {
  content: ""; }

.fa-snapchat-square:before {
  content: ""; }

.fa-pied-piper:before {
  content: ""; }

.fa-first-order:before {
  content: ""; }

.fa-yoast:before {
  content: ""; }

.fa-themeisle:before {
  content: ""; }

.fa-google-plus-circle:before,
.fa-google-plus-official:before {
  content: ""; }

.fa-fa:before,
.fa-font-awesome:before {
  content: ""; }

.fa-handshake-o:before {
  content: ""; }

.fa-envelope-open:before {
  content: ""; }

.fa-envelope-open-o:before {
  content: ""; }

.fa-linode:before {
  content: ""; }

.fa-address-book:before {
  content: ""; }

.fa-address-book-o:before {
  content: ""; }

.fa-vcard:before,
.fa-address-card:before {
  content: ""; }

.fa-vcard-o:before,
.fa-address-card-o:before {
  content: ""; }

.fa-user-circle:before {
  content: ""; }

.fa-user-circle-o:before {
  content: ""; }

.fa-user-o:before {
  content: ""; }

.fa-id-badge:before {
  content: ""; }

.fa-drivers-license:before,
.fa-id-card:before {
  content: ""; }

.fa-drivers-license-o:before,
.fa-id-card-o:before {
  content: ""; }

.fa-quora:before {
  content: ""; }

.fa-free-code-camp:before {
  content: ""; }

.fa-telegram:before {
  content: ""; }

.fa-thermometer-4:before,
.fa-thermometer:before,
.fa-thermometer-full:before {
  content: ""; }

.fa-thermometer-3:before,
.fa-thermometer-three-quarters:before {
  content: ""; }

.fa-thermometer-2:before,
.fa-thermometer-half:before {
  content: ""; }

.fa-thermometer-1:before,
.fa-thermometer-quarter:before {
  content: ""; }

.fa-thermometer-0:before,
.fa-thermometer-empty:before {
  content: ""; }

.fa-shower:before {
  content: ""; }

.fa-bathtub:before,
.fa-s15:before,
.fa-bath:before {
  content: ""; }

.fa-podcast:before {
  content: ""; }

.fa-window-maximize:before {
  content: ""; }

.fa-window-minimize:before {
  content: ""; }

.fa-window-restore:before {
  content: ""; }

.fa-times-rectangle:before,
.fa-window-close:before {
  content: ""; }

.fa-times-rectangle-o:before,
.fa-window-close-o:before {
  content: ""; }

.fa-bandcamp:before {
  content: ""; }

.fa-grav:before {
  content: ""; }

.fa-etsy:before {
  content: ""; }

.fa-imdb:before {
  content: ""; }

.fa-ravelry:before {
  content: ""; }

.fa-eercast:before {
  content: ""; }

.fa-microchip:before {
  content: ""; }

.fa-snowflake-o:before {
  content: ""; }

.fa-superpowers:before {
  content: ""; }

.fa-wpexplorer:before {
  content: ""; }

.fa-meetup:before {
  content: ""; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto; }

sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

button, input, optgroup, select, textarea {
  color: inherit;
  font: inherit;
  margin: 0; }

html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  font-family: Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-weight: 300;
  color: #666;
  font-size: 12px;
  line-height: 1.75em; }
  html input[type=button] {
    -webkit-appearance: button;
    cursor: pointer; }
  html input[disabled] {
    cursor: default; }

body {
  margin: 0;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -moz-font-feature-settings: "liga" on; }

article {
  display: block; }

aside {
  display: block; }

details {
  display: block; }

figcaption {
  display: block; }

figure {
  display: block;
  margin: 1em 40px; }

footer {
  display: block; }

header {
  display: block; }

hgroup {
  display: block; }

main {
  display: block; }

menu {
  display: block; }

nav {
  display: block; }

section {
  display: block; }

summary {
  display: block; }

audio {
  display: inline-block;
  vertical-align: baseline; }
  audio:not([controls]) {
    display: none;
    height: 0; }

canvas {
  display: inline-block;
  vertical-align: baseline; }

progress {
  display: inline-block;
  vertical-align: baseline; }

video {
  display: inline-block;
  vertical-align: baseline; }

[hidden] {
  display: none; }

template {
  display: none; }

a {
  background-color: transparent;
  text-decoration: none;
  color: #1d73a2;
  -webkit-transition: all .2s;
  transition: all .2s;
  margin: 0;
  padding: 0;
  cursor: pointer; }
  a:active {
    outline: 0; }
  a:hover {
    outline: 0;
    color: #175c82; }

abbr[title] {
  border-bottom: 1px dotted; }

b {
  font-weight: 700;
  margin: 0;
  padding: 0; }

strong {
  font-weight: 700;
  margin: 0;
  padding: 0; }

dfn {
  font-style: italic;
  margin: 0;
  padding: 0; }

h1 {
  font-size: 2em;
  margin: .67em 0;
  margin-top: .942400822452556em;
  line-height: 1.130880986943067em;
  margin-bottom: .188480164490511em;
  margin: 0;
  padding: 0;
  font-family: Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-weight: 500;
  color: #111;
  clear: both; }

mark {
  background: #ff0;
  color: #000; }

small {
  font-size: 80%;
  margin: 0;
  padding: 0;
  line-height: 0; }

sub {
  bottom: -.25em;
  margin: 0;
  padding: 0;
  line-height: 0; }

sup {
  top: -.5em;
  margin: 0;
  padding: 0;
  line-height: 0; }
  sup a .fa {
    font-size: 1em; }

img {
  border: 0;
  margin: 0;
  padding: 0; }

hr {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  height: 0; }

pre {
  overflow: auto;
  padding: .875em;
  margin-bottom: 1.75em;
  background: #222;
  line-height: 1;
  color: #ffff00;
  border-radius: 3px;
  font-size: 10px;
  font-family: monospace;
  font-size: 1em;
  margin: 0;
  padding: 0;
  margin-bottom: 1.75em; }
  pre code {
    padding: 0; }

code {
  font-family: monospace;
  font-size: 1em;
  margin: 0;
  padding: 0;
  font-family: Courier New, Courier, Lucida Sans Typewriter, Lucida Typewriter, monospace;
  padding: .0875em .2625em;
  line-height: 0; }

kbd {
  font-family: monospace;
  font-size: 1em;
  margin: 0;
  padding: 0; }

samp {
  font-family: monospace;
  font-size: 1em;
  margin: 0;
  padding: 0; }

button {
  overflow: visible;
  text-transform: none;
  -webkit-appearance: button;
  cursor: pointer;
  display: block;
  cursor: pointer;
  font-size: 12px;
  padding: .4375em 1.75em;
  margin-bottom: 1.18125em; }

input {
  line-height: normal; }
  input:focus {
    background: #ffff00; }

optgroup {
  font-weight: 700; }

select {
  text-transform: none;
  outline: none;
  border: 1px solid #dddddd;
  border-radius: 3px;
  padding: 10px 12px;
  width: calc(100% - 24px);
  margin: 0 auto 1em;
  width: 100%;
  height: 35px; }

textarea {
  overflow: auto;
  display: block;
  max-width: 100%;
  padding: .4375em;
  font-size: 12px;
  margin-bottom: 1.18125em;
  outline: none;
  border: 1px solid #dddddd;
  border-radius: 3px;
  padding: 10px 12px;
  width: calc(100% - 24px);
  margin: 0 auto 1em; }

input[type=reset] {
  -webkit-appearance: button;
  cursor: pointer; }

input[type=submit] {
  -webkit-appearance: button;
  cursor: pointer;
  display: block;
  cursor: pointer;
  font-size: 12px;
  padding: .4375em 1.75em;
  margin-bottom: 1.18125em; }

button[disabled] {
  cursor: default; }

button::-moz-focus-inner {
  border: 0;
  padding: 0; }

input::-moz-focus-inner {
  border: 0;
  padding: 0; }

input[type=checkbox] {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0; }

input[type=radio] {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0; }

input[type=number]::-webkit-inner-spin-button {
  height: auto; }

input[type=number]::-webkit-outer-spin-button {
  height: auto; }

input[type=search] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
          box-sizing: content-box; }

input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none; }

input[type=search]::-webkit-search-decoration {
  -webkit-appearance: none; }

fieldset {
  border: 1px solid silver;
  margin: 0 2px;
  padding: .35em .625em .75em;
  padding: .875em 1.75em 1.75em;
  border-width: 1px;
  border-style: solid;
  max-width: 100%;
  margin-bottom: 1.8375em;
  margin: 0;
  padding: 0; }
  fieldset button {
    margin-bottom: 0; }
  fieldset input[type=submit] {
    margin-bottom: 0; }

legend {
  border: 0;
  padding: 0;
  color: #111;
  font-weight: 700;
  margin: 0;
  padding: 0; }

table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
  margin-bottom: 2.1875em;
  margin: 0;
  padding: 0;
  margin-bottom: 1.75em; }

td {
  padding: 0;
  margin: 0;
  padding: 0;
  padding: .21875em .875em; }

th {
  padding: 0;
  margin: 0;
  padding: 0;
  text-align: left;
  color: #111;
  padding: .21875em .875em; }

@media (min-width: 600px) {
  html {
    font-size: 12px; }

  h1 {
    font-size: calc(27.85438995234061px +18.56959 *((100vw - 600px) / 540)); }

  h2 {
    font-size: calc(23.53700340860508px +15.69134 *((100vw - 600px) / 540)); }

  h3 {
    font-size: calc(19.888804974891777px +13.2592 *((100vw - 600px) / 540)); }

  h4 {
    font-size: calc(16.806071548796314px +11.20405 *((100vw - 600px) / 540)); }

  h5 {
    font-size: calc(14.201156945318074px +9.46744 *((100vw - 600px) / 540)); }

  h6 {
    font-size: calc(12px +8 *((100vw - 600px) / 540)); } }
abbr {
  margin: 0;
  padding: 0;
  border-bottom: 1px dotted currentColor;
  cursor: help; }

acronym {
  margin: 0;
  padding: 0;
  border-bottom: 1px dotted currentColor;
  cursor: help; }

address {
  margin: 0;
  padding: 0;
  margin-bottom: 1.75em;
  font-style: normal; }

big {
  margin: 0;
  padding: 0;
  line-height: 0; }

blockquote {
  margin: 0;
  padding: 0;
  margin-bottom: 1.75em;
  font-style: italic; }
  blockquote cite {
    display: block;
    font-style: normal; }

caption {
  margin: 0;
  padding: 0; }

center {
  margin: 0;
  padding: 0; }

cite {
  margin: 0;
  padding: 0; }

dd {
  margin: 0;
  padding: 0; }

del {
  margin: 0;
  padding: 0; }

dl {
  margin: 0;
  padding: 0;
  margin-bottom: 1.75em; }

dt {
  margin: 0;
  padding: 0;
  color: #111;
  font-weight: 700; }

em {
  margin: 0;
  padding: 0; }

form {
  margin: 0;
  padding: 0; }

h2 {
  margin: 0;
  padding: 0;
  font-family: Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-weight: 500;
  color: #111;
  clear: both;
  font-size: 23.53700340860508px;
  margin-top: 1.115265165420465em;
  line-height: 1.338318198504558em;
  margin-bottom: .251483121980101em; }

h3 {
  margin: 0;
  padding: 0;
  font-family: Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-weight: 500;
  color: #111;
  clear: both;
  font-size: 19.888804974891777px;
  margin-top: 1.319837970815179em;
  line-height: 1.583805564978215em;
  margin-bottom: .303784103173448em; }

h4 {
  margin: 0;
  padding: 0;
  font-family: Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-weight: 500;
  color: #111;
  clear: both;
  font-size: 16.806071548796314px;
  margin-top: 1.561935513828041em;
  line-height: 1.87432261659365em;
  margin-bottom: .368150361036632em; }

h5 {
  margin: 0;
  padding: 0;
  font-family: Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-weight: 500;
  color: #111;
  clear: both;
  font-size: 14.201156945318074px;
  margin-top: 1.84844094752817em;
  line-height: 2.218129137033805em;
  margin-bottom: .369688189505634em; }

h6 {
  margin: 0;
  padding: 0;
  font-family: Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-weight: 500;
  color: #111;
  clear: both;
  font-size: 12px;
  margin-top: 2.1875em;
  line-height: 2.625em;
  margin-bottom: .619791666666667em; }

i {
  margin: 0;
  padding: 0; }

ins {
  margin: 0;
  padding: 0; }

label {
  margin: 0;
  padding: 0;
  display: block;
  padding-bottom: .21875em;
  margin-bottom: -.21875em;
  cursor: pointer; }

li {
  margin: 0;
  padding: 0; }

ol {
  margin: 0;
  padding: 0;
  margin-bottom: 1.75em;
  padding-left: 1.4em; }

p {
  margin: 0;
  padding: 0;
  margin-bottom: 1.75em;
  margin: 0 0 1rem 0; }

q {
  margin: 0;
  padding: 0; }

s {
  margin: 0;
  padding: 0; }

strike {
  margin: 0;
  padding: 0; }

tbody {
  margin: 0;
  padding: 0; }

tfoot {
  margin: 0;
  padding: 0; }

thead {
  margin: 0;
  padding: 0; }

tr {
  margin: 0;
  padding: 0; }

tt {
  margin: 0;
  padding: 0; }

u {
  margin: 0;
  padding: 0; }

ul {
  margin: 0;
  padding: 0;
  margin-bottom: 1.75em;
  padding-left: 1.1em; }

var {
  margin: 0;
  padding: 0; }

@media (min-width: 1140px) {
  h1 {
    font-size: 46.423983253901014px;
    margin-top: .942400822452556em;
    line-height: 1.130880986943067em;
    margin-bottom: .188480164490511em; }

  h2 {
    font-size: 39.228339014341806px;
    margin-top: 1.115265165420465em;
    line-height: 1.338318198504558em;
    margin-bottom: .240111086421698em; }

  h3 {
    font-size: 33.14800829148629px;
    margin-top: 1.319837970815179em;
    line-height: 1.583805564978215em;
    margin-bottom: .287857499569283em; }

  h4 {
    font-size: 28.01011924799386px;
    margin-top: 1.561935513828041em;
    line-height: 1.87432261659365em;
    margin-bottom: .345845057728222em; }

  h5 {
    font-size: 23.668594908863454px;
    margin-top: 1.84844094752817em;
    line-height: 2.218129137033805em;
    margin-bottom: .369688189505634em; }

  h6 {
    font-size: 20px;
    margin-top: 2.1875em;
    line-height: 2.625em;
    margin-bottom: .473958333333333em; }

  fieldset {
    margin-bottom: 2.078125em; }

  input[type=email] {
    font-size: 20px;
    margin-bottom: .5140625em; }

  input[type=password] {
    font-size: 20px;
    margin-bottom: .5140625em; }

  input[type=text] {
    font-size: 20px;
    margin-bottom: .5140625em; }

  textarea {
    font-size: 20px;
    margin-bottom: .5140625em; }

  button {
    font-size: 20px;
    margin-bottom: 1.3125em; }

  input[type=submit] {
    font-size: 20px;
    margin-bottom: 1.3125em; }

  table {
    margin-bottom: 2.05625em; }

  th {
    padding: .4375em .875em; }

  td {
    padding: .4375em .875em; } }
input[type=email] {
  display: block;
  max-width: 100%;
  padding: .4375em;
  font-size: 12px;
  margin-bottom: 1.18125em; }

input[type=password] {
  display: block;
  max-width: 100%;
  padding: .4375em;
  font-size: 12px;
  margin-bottom: 1.18125em; }

input[type=text] {
  display: block;
  max-width: 100%;
  padding: .4375em;
  font-size: 12px;
  margin-bottom: 1.18125em; }

.master {
  background-image: url("../img/background.png");
  background-size: cover;
  background-position: top;
  min-height: 100vh;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -ms-flex-pack: center;
  -webkit-box-pack: center;
          justify-content: center;
  -ms-flex-align: center;
  -webkit-box-align: center;
          align-items: center; }

.box {
  width: 450px;
  border-radius: 0 0 3px 3px;
  overflow: hidden;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0 10px 10px rgba(0, 0, 0, 0.19), 0 6px 3px rgba(0, 0, 0, 0.23);
          box-shadow: 0 10px 10px rgba(0, 0, 0, 0.19), 0 6px 3px rgba(0, 0, 0, 0.23); }

.header {
  background-color: #357295;
  padding: 30px 30px 40px;
  border-radius: 3px 3px 0 0;
  text-align: center; }

.header__step {
  font-weight: 300;
  text-transform: uppercase;
  font-size: 14px;
  letter-spacing: 1.1px;
  margin: 0 0 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: #fff; }

.header__title {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: #fff;
  font-weight: 400;
  font-size: 20px;
  margin: 0 0 15px; }

.step {
  position: relative;
  z-index: 1;
  padding-left: 0;
  list-style: none;
  margin-bottom: 0;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -ms-flex-direction: row-reverse;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
          flex-direction: row-reverse;
  -ms-flex-pack: center;
  -webkit-box-pack: center;
          justify-content: center;
  -ms-flex-align: center;
  -webkit-box-align: center;
          align-items: center;
  margin-top: -20px; }

.step__divider {
  background-color: #cacfd2;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 60px;
  height: 3px; }
  .step__divider:first-child {
    -ms-flex: 1 0 auto;
    -webkit-box-flex: 1;
            flex: 1 0 auto; }
  .step__divider:last-child {
    -ms-flex: 1 0 auto;
    -webkit-box-flex: 1;
            flex: 1 0 auto; }

.step__icon {
  background-color: #cacfd2;
  font-style: normal;
  width: 40px;
  height: 40px;
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -ms-flex-pack: center;
  -webkit-box-pack: center;
          justify-content: center;
  -ms-flex-align: center;
  -webkit-box-align: center;
          align-items: center;
  border-radius: 50%;
  color: #fff; }
  .step__icon.welcome:before {
    content: '\f144';
    font-family: Ionicons; }
  .step__icon.requirements:before {
    content: '\f127';
    font-family: Ionicons; }
  .step__icon.permissions:before {
    content: '\f296';
    font-family: Ionicons; }
  .step__icon.database:before {
    content: '\f454';
    font-family: Ionicons; }
  .step__icon.update:before {
    content: '\f2bf';
    font-family: Ionicons; }

.main {
  margin-top: -20px;
  background-color: #fff;
  border-radius: 0 0 3px 3px;
  padding: 40px 40px 30px; }

.buttons {
  text-align: center; }
  .buttons .button {
    margin: .5em; }

.buttons--right {
  text-align: right; }

.button {
  display: inline-block;
  background-color: #34a0db;
  border-radius: 2px;
  padding: 7px 20px;
  color: #fff;
  -webkit-box-shadow: 0 1px 1.5px rgba(0, 0, 0, 0.12), 0 1px 1px rgba(0, 0, 0, 0.24);
          box-shadow: 0 1px 1.5px rgba(0, 0, 0, 0.12), 0 1px 1px rgba(0, 0, 0, 0.24);
  text-decoration: none;
  outline: none;
  border: none;
  -webkit-transition: background-color .2s ease, -webkit-box-shadow .2s ease;
  transition: background-color .2s ease, -webkit-box-shadow .2s ease;
  transition: box-shadow .2s ease, background-color .2s ease;
  transition: box-shadow .2s ease, background-color .2s ease, -webkit-box-shadow .2s ease;
  cursor: pointer; }
  .button:hover {
    color: #fff;
    -webkit-box-shadow: 0 10px 10px rgba(0, 0, 0, 0.19), 0 6px 3px rgba(0, 0, 0, 0.23);
            box-shadow: 0 10px 10px rgba(0, 0, 0, 0.19), 0 6px 3px rgba(0, 0, 0, 0.23);
    background-color: #2490cb; }

.button--light {
  padding: 3px 16px;
  font-size: 16px;
  border-top: 1px solid #eee;
  color: #222;
  background: #fff; }
  .button--light:hover {
    color: #222;
    background: #fff;
    -webkit-box-shadow: 0 3px 3px rgba(0, 0, 0, 0.16), 0 3px 3px rgba(0, 0, 0, 0.23);
            box-shadow: 0 3px 3px rgba(0, 0, 0, 0.16), 0 3px 3px rgba(0, 0, 0, 0.23); }

.list {
  padding-left: 0;
  list-style: none;
  margin-bottom: 0;
  margin: 20px 0 35px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 2px; }
  .list .list__item.list__title {
    background: rgba(0, 0, 0, 0.12); }
    .list .list__item.list__title.success span {
      color: #008000; }
    .list .list__item.list__title.success .fa:before {
      color: #008000; }
    .list .list__item.list__title.error span {
      color: #ff0000; }
    .list .list__item.list__title.error .fa:before {
      color: #ff0000; }

.list__item {
  position: relative;
  overflow: hidden;
  padding: 7px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12); }
  .list__item:first-letter {
    text-transform: uppercase; }
  .list__item:last-child {
    border-bottom: none; }
  .list__item .fa.row-icon:before {
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -ms-flex-pack: center;
    -webkit-box-pack: center;
            justify-content: center;
    -ms-flex-align: center;
    -webkit-box-align: center;
            align-items: center;
    padding: 7px 20px;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0; }
  .list__item.success .fa:before {
    color: #2ecc71; }
  .list__item.error .fa:before {
    color: #e74c3c; }

.list__item--permissions:before {
  content: '' !important; }
.list__item--permissions span {
  display: -ms-flexbox;
  display: -webkit-box;
  display: flex;
  -ms-flex-pack: center;
  -webkit-box-pack: center;
          justify-content: center;
  -ms-flex-align: center;
  -webkit-box-align: center;
          align-items: center;
  padding: 7px 20px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: #f5f5f5;
  font-weight: 700;
  font-size: 16px; }
  .list__item--permissions span:before {
    margin-right: 7px;
    font-weight: 400; }
.list__item--permissions.success i:before {
  color: #2ecc71;
  vertical-align: 1px; }
.list__item--permissions.error i:before {
  color: #e74c3c;
  vertical-align: 1px; }

.textarea {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  font-size: 14px;
  line-height: 25px;
  height: 150px;
  outline: none;
  border: 1px solid rgba(0, 0, 0, 0.2); }
  .textarea ~ .button {
    margin-bottom: 35px; }

.text-center {
  text-align: center; }

.form-control {
  height: 14px;
  width: 100%; }

.has-error {
  color: #ff0000; }
  .has-error input {
    color: #000000;
    border: 1px solid red; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

input[type='text'] {
  outline: none;
  border: 1px solid #dddddd;
  border-radius: 3px;
  padding: 10px 12px;
  width: calc(100% - 24px);
  margin: 0 auto 1em; }

input[type='password'] {
  outline: none;
  border: 1px solid #dddddd;
  border-radius: 3px;
  padding: 10px 12px;
  width: calc(100% - 24px);
  margin: 0 auto 1em; }

input[type='url'] {
  outline: none;
  border: 1px solid #dddddd;
  border-radius: 3px;
  padding: 10px 12px;
  width: calc(100% - 24px);
  margin: 0 auto 1em; }

input[type='number'] {
  outline: none;
  border: 1px solid #dddddd;
  border-radius: 3px;
  padding: 10px 12px;
  width: calc(100% - 24px);
  margin: 0 auto 1em; }

.tabs {
  padding: 0; }
  .tabs .tab-input {
    display: none; }
    .tabs .tab-input:checked + .tab-label {
      background-color: #fff;
      color: #333; }
  .tabs .tab-label {
    color: #ddd;
    cursor: pointer;
    float: left;
    padding: 1em;
    text-align: center;
    -webkit-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out; }
    .tabs .tab-label:hover {
      color: #333; }
  .tabs .tabs-wrap {
    clear: both; }
  .tabs .tab {
    display: none; }
    .tabs .tab > *:last-child {
      margin-bottom: 0; }

.float-left {
  float: left; }

.float-right {
  float: right; }

.buttons-container {
  min-height: 37px;
  margin: 1em 0 0; }

.block {
  -webkit-box-shadow: 0 3px 1px darkgray;
          box-shadow: 0 3px 1px darkgray; }
  .block input[type='radio'] {
    width: 100%;
    display: none; }
    .block input[type='radio'] + label {
      background: #008080;
      color: #fff;
      padding-top: 5px;
      -webkit-transition: all 0.1s ease-in-out;
      transition: all 0.1s ease-in-out; }
      .block input[type='radio'] + label:hover {
        background: #144242;
        color: #fff;
        padding-top: 5px; }
    .block input[type='radio']:checked + label {
      background-color: #a9a9a9; }
    .block input[type='radio']:checked ~ .info {
      height: 200px;
      overflow-y: auto;
      -webkit-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out; }
    .block input[type='radio'] ~ .info > div {
      padding-top: 15px; }
  .block label {
    width: 450px;
    max-width: 100%;
    cursor: pointer; }
  .block span {
    font-family: Arial;
    font-weight: 700;
    display: block;
    padding: 10px 12px 12px 15px;
    margin: 0;
    cursor: pointer; }

.info {
  background: #fff;
  color: #222;
  width: 100%;
  height: 0;
  line-height: 2;
  padding-left: 15px;
  padding-right: 15px;
  display: block;
  overflow: hidden;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-transition: .3s ease-out;
  transition: .3s ease-out; }

::-moz-selection {
  background: #222;
  color: #fff; }

::selection {
  background: #222;
  color: #fff; }

::-webkit-scrollbar {
  width: 12px; }

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  border-radius: 0; }

::-webkit-scrollbar-thumb {
  border-radius: 0;
  background: #ccc;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3); }

.margin-bottom-1 {
  margin-bottom: 1em; }

.margin-bottom-2 {
  margin-bottom: 1em; }

.margin-top-1 {
  margin-top: 1em; }

.margin-top-2 {
  margin-top: 1em; }

.alert {
  margin: 0 0 10px;
  font-size: 1.1em;
  background-color: #f5f5f5;
  border-radius: 3px;
  padding: 10px;
  position: relative; }
  .alert.alert-danger {
    background: #ff0000;
    color: #ffffff;
    padding: 10px 20px 15px; }
    .alert.alert-danger h4 {
      color: #ffffff;
      margin: 0; }
    .alert.alert-danger ul {
      margin: 0; }
  .alert .close {
    width: 25px;
    height: 25px;
    padding: 0;
    margin: 0;
    -webkit-box-shadow: none;
            box-shadow: none;
    border: 2px solid red;
    outline: none;
    float: right;
    border-radius: 50%;
    position: absolute;
    right: 0;
    top: 0;
    background-color: transparent;
    cursor: pointer;
    -webkit-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out; }
    .alert .close:hover {
      background-color: #ffffff;
      color: #ff0000; }

svg:not(:root) {
  overflow: hidden; }

.step__item.active .step__icon,
.step__item.active ~ .step__divider,
.step__item.active ~ .step__item .step__icon {
  background-color: #34a0db;
  -webkit-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out; }

.step__item.active .step__icon:hover,
.step__item.active ~ .step__item .step__icon:hover {
  background-color: #3d657b; }

.form-group.has-error select {
  border-color: #ff0000; }
.form-group.has-error textarea {
  border-color: #ff0000; }
.form-group.has-error input[type='text'] {
  border-color: #ff0000; }
.form-group.has-error input[type='password'] {
  border-color: #ff0000; }
.form-group.has-error input[type='url'] {
  border-color: #ff0000; }
.form-group.has-error input[type='number'] {
  border-color: #ff0000; }
.form-group.has-error .error-block {
  margin: -12px 0 0;
  display: block;
  width: 100%;
  font-size: .9em;
  color: #ff0000;
  font-weight: 500; }

.tabs-full .tab-label {
  display: table-cell;
  float: none;
  width: 1%; }

#tab1:checked ~ .tabs-wrap #tab1content {
  display: block; }

#tab2:checked ~ .tabs-wrap #tab2content {
  display: block; }

#tab3:checked ~ .tabs-wrap #tab3content {
  display: block; }

#tab4:checked ~ .tabs-wrap #tab4content {
  display: block; }

#tab5:checked ~ .tabs-wrap #tab5content {
  display: block; }

.github img {
  position: absolute;
  top: 0;
  right: 0;
  border: 0; }

#tab3content .block:first-child {
  border-radius: 3px 3px 0 0; }
#tab3content .block:last-child {
  border-radius: 0 0 3px 3px; }

/*# sourceMappingURL=style.css.map */