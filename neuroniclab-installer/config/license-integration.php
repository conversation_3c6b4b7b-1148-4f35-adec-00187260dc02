<?php

return [

    /*
    |--------------------------------------------------------------------------
    | License Verification Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration file contains settings for license verification
    | integration with the NeuronicLab Installer package.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | License API Configuration
    |--------------------------------------------------------------------------
    |
    | Configure your license API credentials and item information.
    |
    */
    'api' => [
        'personal_token' => app('encrypter')->decrypt(file_get_contents(storage_path('app/secure/license_token.enc'))),
        'base_url' => 'https://api.envato.com/v3/market',
        'timeout' => 30, // API request timeout in seconds
        'retry_attempts' => 3,
        'retry_delay' => 1000, // Delay between retries in milliseconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Item Configuration
    |--------------------------------------------------------------------------
    |
    | Configure your Envato marketplace item details.
    |
    */
    'item' => [
        'id' => '12345678', // Your actual item ID
        'name' => 'Your Product Name',
        'marketplace' => 'codecanyon', // codecanyon, themeforest, etc.
        'author_username' => 'your_username',
        'item_url' => 'https://codecanyon.net/item/your-item/12345678',
        'support_url' => 'https://your-support-site.com',
    ],

    /*
    |--------------------------------------------------------------------------
    | License Validation Rules
    |--------------------------------------------------------------------------
    |
    | Configure validation rules for license verification.
    |
    */
    'validation' => [
        'validate_buyer_email' => false,
        'validate_buyer_username' => false,
        'allow_multiple_domains' => false,
        'max_domains_regular' => 1,
        'max_domains_extended' => 5,
        'license_expiry_months' => 12,
        'grace_period_days' => 7,
    ],

    /*
    |--------------------------------------------------------------------------
    | Caching Configuration
    |--------------------------------------------------------------------------
    |
    | Configure caching for license verification to reduce API calls.
    |
    */
    'cache' => [
        'enabled' => true,
        'ttl' => 86400, // Cache TTL in seconds (24 hours)
        'prefix' => 'license_verification_',
        'store' => 'default',
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Configure security settings for license verification.
    |
    */
    'security' => [
        'rate_limit' => [
            'enabled' => true,
            'max_attempts' => 5,
            'decay_minutes' => 1,
        ],
        'ip_whitelist' => [
            'enabled' => false,
            'allowed_ips' => [],
        ],
        'encryption' => [
            'encrypt_stored_data' => true,
            'hash_purchase_codes' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    |
    | Configure database settings for license tracking.
    |
    */
    'database' => [
        'track_usage' => true,
        'table_name' => 'license_verifications',
        'analytics_table' => 'license_analytics',
        'cleanup_old_records' => true,
        'cleanup_after_days' => 365,
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Configuration
    |--------------------------------------------------------------------------
    |
    | Configure notifications for license events.
    |
    */
    'notifications' => [
        'enabled' => true,
        'channels' => ['mail', 'database'],
        'events' => [
            'license_verified' => true,
            'license_expired' => true,
            'license_expiring' => true,
            'invalid_attempt' => true,
            'multiple_domain_usage' => true,
        ],
        'admin_email' => '<EMAIL>',
        'expiry_warning_days' => [30, 7, 1], // Days before expiry to send warnings
    ],

    /*
    |--------------------------------------------------------------------------
    | Fallback Configuration
    |--------------------------------------------------------------------------
    |
    | Configure fallback behavior when Envato API is unavailable.
    |
    */
    'fallback' => [
        'enabled' => true,
        'use_cache' => true,
        'cache_validity_days' => 7,
        'offline_mode' => false,
        'offline_grace_period' => 24,
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configure logging for license verification activities.
    |
    */
    'logging' => [
        'enabled' => true,
        'level' => 'info',
        'channel' => 'default',
        'log_successful_verifications' => true,
        'log_failed_attempts' => true,
        'log_api_calls' => false,
        'anonymize_purchase_codes' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Configuration
    |--------------------------------------------------------------------------
    |
    | Configure development and testing settings.
    |
    */
    'development' => [
        'debug_mode' => false,
        'mock_api' => false,
        'test_purchase_codes' => [
            'valid_regular' => 'test-regular-license-code',
            'valid_extended' => 'test-extended-license-code',
            'invalid' => 'invalid-test-code',
        ],
        'bypass_verification' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | License Types Configuration
    |--------------------------------------------------------------------------
    |
    | Configure different license types and their permissions.
    |
    */
    'license_types' => [
        'regular' => [
            'name' => 'Regular License',
            'max_domains' => 1,
            'commercial_use' => false,
            'resale_allowed' => false,
            'support_included' => true,
            'updates_included' => true,
        ],
        'extended' => [
            'name' => 'Extended License',
            'max_domains' => 5,
            'commercial_use' => true,
            'resale_allowed' => false,
            'support_included' => true,
            'updates_included' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Messages
    |--------------------------------------------------------------------------
    |
    | Customize error messages for different scenarios.
    |
    */
    'messages' => [
        'invalid_purchase_code' => 'Invalid purchase code. Please check and try again.',
        'item_mismatch' => 'This purchase code is not valid for this product.',
        'already_used' => 'This license is already in use on another domain.',
        'expired' => 'This license has expired. Please renew your license.',
        'api_unavailable' => 'License verification service is temporarily unavailable.',
        'rate_limit_exceeded' => 'Too many verification attempts. Please try again later.',
        'email_mismatch' => 'Email address does not match the license buyer.',
        'username_mismatch' => 'Username does not match the license buyer.',
        'max_domains_exceeded' => 'Maximum number of domains exceeded for this license type.',
        'verification_failed' => 'License verification failed. Please contact support.',
        'success' => 'License verified successfully.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    |
    | Configure integration with NeuronicLab Installer.
    |
    */
    'installer_integration' => [
        'enabled' => true,
        'verification_endpoint' => '/api/verify-license',
        'verification_file' => 'storage/app/secure/license_verified',
        'custom_views' => true,
        'custom_validation' => true,
        'middleware' => ['throttle:5,1'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Configuration
    |--------------------------------------------------------------------------
    |
    | Configure analytics and reporting for license usage.
    |
    */
    'analytics' => [
        'enabled' => true,
        'track_ip_addresses' => true,
        'track_user_agents' => true,
        'track_verification_attempts' => true,
        'track_domain_changes' => true,
        'generate_reports' => true,
        'report_frequency' => 'monthly', // daily, weekly, monthly
    ],

];
