# NeuronicLab Installer - API Documentation

## Overview

This document provides comprehensive API documentation for the NeuronicLab Installer package, including classes, methods, configuration options, and extension points.

## Package Structure

```
NeuronicLab\Installer\
├── Providers\
│   └── InstallerServiceProvider
├── Controllers\
│   ├── WelcomeController
│   ├── RequirementsController
│   ├── PermissionsController
│   ├── LicenseController
│   ├── EnvironmentController
│   ├── DatabaseController
│   └── FinalController
├── Middleware\
│   ├── CanInstall
│   └── Security
├── Commands\
│   ├── InstallCommand
│   └── PublishCommand
└── Helpers\
    └── functions.php
```

## Service Provider

### InstallerServiceProvider

**Namespace**: `NeuronicLab\Installer\Providers\InstallerServiceProvider`

#### Methods

##### `register()`
Registers the package services and configuration.

```php
public function register(): void
```

##### `boot(Router $router)`
Bootstraps the package services, routes, views, and middleware.

```php
public function boot(Router $router): void
```

**Registered Services:**
- Configuration merging
- Route loading
- View loading
- Translation loading
- Middleware registration
- Asset publishing

## Controllers

### WelcomeController

**Namespace**: `NeuronicLab\Installer\Controllers\WelcomeController`

#### Methods

##### `welcome()`
Displays the installer welcome page.

```php
public function welcome(): \Illuminate\View\View
```

**Returns**: Welcome view with package information

---

### RequirementsController

**Namespace**: `NeuronicLab\Installer\Controllers\RequirementsController`

#### Methods

##### `requirements()`
Displays the system requirements check page.

```php
public function requirements(): \Illuminate\View\View
```

**Returns**: Requirements view with PHP version and extension checks

---

### PermissionsController

**Namespace**: `NeuronicLab\Installer\Controllers\PermissionsController`

#### Methods

##### `permissions()`
Displays the file permissions check page.

```php
public function permissions(): \Illuminate\View\View
```

**Returns**: Permissions view with directory permission status

---

### LicenseController

**Namespace**: `NeuronicLab\Installer\Controllers\LicenseController`

#### Methods

##### `license()`
Displays the license verification page.

```php
public function license(): \Illuminate\View\View
```

**Returns**: License verification form view

##### `licenseCheck(Request $request)`
Processes license verification.

```php
public function licenseCheck(Request $request): \Illuminate\Http\RedirectResponse
```

**Parameters:**
- `$request`: HTTP request containing license data

**Validation Rules:**
- `email`: required|email
- `username`: required|string
- `purchase_code`: required|string

**Returns**: Redirect response with verification result

##### `verifyLicense(Request $request)`
Performs remote license verification.

```php
protected function verifyLicense(Request $request): bool
```

**Parameters:**
- `$request`: HTTP request with license credentials

**Returns**: Boolean verification result

##### `createVerificationFile()`
Creates local verification file after successful verification.

```php
protected function createVerificationFile(): void
```

##### `isLicenseVerified()`
Checks if license is already verified.

```php
public static function isLicenseVerified(): bool
```

**Returns**: Boolean verification status

---

### EnvironmentController

**Namespace**: `NeuronicLab\Installer\Controllers\EnvironmentController`

#### Methods

##### `environmentMenu()`
Displays environment configuration menu.

```php
public function environmentMenu(): \Illuminate\View\View
```

##### `environmentWizard()`
Displays environment configuration wizard.

```php
public function environmentWizard(): \Illuminate\View\View
```

**Returns**: Environment wizard view with current configuration

##### `environmentClassic()`
Displays classic environment editor.

```php
public function environmentClassic(): \Illuminate\View\View
```

##### `saveWizard(Request $request, Redirector $redirect)`
Processes environment configuration from wizard.

```php
public function saveWizard(Request $request, Redirector $redirect): \Illuminate\Http\RedirectResponse
```

**Parameters:**
- `$request`: HTTP request with environment data
- `$redirect`: Laravel redirector instance

**Validation Rules:**
- `app_name`: required|string|max:50
- `app_debug`: required|string
- `app_url`: required|url
- `database_hostname`: required|string|max:50
- `database_name`: required|string|max:50
- `database_username`: required|string|max:50
- `database_password`: nullable|string|max:50

##### `saveClassic(Request $request, Redirector $redirect)`
Processes environment configuration from classic editor.

```php
public function saveClassic(Request $request, Redirector $redirect): \Illuminate\Http\RedirectResponse
```

##### `checkDatabaseConnection(Request $request)`
Tests database connection with provided credentials.

```php
private function checkDatabaseConnection(Request $request): bool
```

##### `importDatabaseSchema(Request $request)`
Imports database schema from SQL file.

```php
protected function importDatabaseSchema(Request $request): void
```

---

### DatabaseController

**Namespace**: `NeuronicLab\Installer\Controllers\DatabaseController`

#### Methods

##### `database()`
Handles database migration and seeding.

```php
public function database(): \Illuminate\Http\RedirectResponse
```

---

### FinalController

**Namespace**: `NeuronicLab\Installer\Controllers\FinalController`

#### Methods

##### `finish(InstalledFileManager $fileManager, FinalInstallManager $finalInstall, EnvironmentManager $environment)`
Completes installation and displays final page.

```php
public function finish(
    InstalledFileManager $fileManager,
    FinalInstallManager $finalInstall,
    EnvironmentManager $environment
): \Illuminate\View\View
```

## Middleware

### CanInstall

**Namespace**: `NeuronicLab\Installer\Middleware\CanInstall`

#### Methods

##### `handle($request, Closure $next)`
Checks if installation is allowed.

```php
public function handle($request, Closure $next): mixed
```

**Parameters:**
- `$request`: HTTP request
- `$next`: Next middleware closure

**Returns**: Response or redirect based on installation status

##### `alreadyInstalled()`
Checks if application is already installed.

```php
public function alreadyInstalled(): bool
```

---

### Security

**Namespace**: `NeuronicLab\Installer\Middleware\Security`

#### Methods

##### `handle($request, Closure $next)`
Handles security checks during installation.

```php
public function handle($request, Closure $next): mixed
```

## Commands

### InstallCommand

**Namespace**: `NeuronicLab\Installer\Commands\InstallCommand`

#### Properties

- `$signature`: `'neuroniclab:install {--force : Overwrite existing files}'`
- `$description`: `'Install NeuronicLab Installer package'`

#### Methods

##### `handle()`
Executes the installation command.

```php
public function handle(): int
```

---

### PublishCommand

**Namespace**: `NeuronicLab\Installer\Commands\PublishCommand`

#### Properties

- `$signature`: `'neuroniclab:publish {--tag=* : Specific tags to publish} {--force : Overwrite existing files}'`
- `$description`: `'Publish NeuronicLab Installer assets and configuration'`

#### Methods

##### `handle()`
Executes the publishing command.

```php
public function handle(): int
```

## Configuration API

### Configuration Structure

```php
// config/neuroniclab-installer.php
return [
    'core' => [
        'minPhpVersion' => '8.1',
    ],
    
    'requirements' => [
        'php' => [...],
        'apache' => [...],
    ],
    
    'permissions' => [...],
    
    'environment' => [
        'form' => [
            'rules' => [...],
        ],
    ],
    
    'license' => [
        'enabled' => true,
        'verification_file' => 'vendor/mockery/mockery/verified',
        'verification_url' => null,
        'item_id' => null,
        'item_name' => 'NeuronicLab',
        'required_fields' => [...],
    ],
    
    'database' => [
        'schema_file' => 'database/neuroniclab-installer/schema.sql',
        'mysql_path' => 'mysql',
        'import_method' => 'auto',
    ],
    
    'ui' => [
        'app_name' => 'NeuronicLab',
        'logo' => null,
        'primary_color' => '#007bff',
        'background_image' => null,
        'custom_css' => null,
    ],
    
    'steps' => [
        'welcome' => true,
        'requirements' => true,
        'permissions' => true,
        'license' => true,
        'environment' => true,
        'database' => true,
        'final' => true,
    ],
];
```

### Configuration Methods

#### Accessing Configuration

```php
// Get entire configuration
$config = config('neuroniclab-installer');

// Get specific sections
$uiConfig = config('neuroniclab-installer.ui');
$licenseConfig = config('neuroniclab-installer.license');
$databaseConfig = config('neuroniclab-installer.database');

// Get specific values
$appName = config('neuroniclab-installer.ui.app_name');
$licenseEnabled = config('neuroniclab-installer.license.enabled');
$schemaFile = config('neuroniclab-installer.database.schema_file');
```

#### Runtime Configuration

```php
// Modify configuration at runtime
config(['neuroniclab-installer.ui.app_name' => 'Custom App Name']);
config(['neuroniclab-installer.license.enabled' => false]);
```

## Helper Functions

### Route Helpers

#### `isActive($route, $output = "active")`
Sets active class for current route.

```php
function isActive(string $route, string $output = "active"): ?string
```

**Parameters:**
- `$route`: Route name to check
- `$output`: CSS class to return if active

**Returns**: CSS class string or null

#### `isActiveUrl($url, $output = "active")`
Sets active class for current URL.

```php
function isActiveUrl(string $url, string $output = "active"): ?string
```

## Events

### Installation Events

The package fires several Laravel events during installation:

#### `EnvironmentSaved`
Fired when environment configuration is saved.

```php
use RachidLaasri\LaravelInstaller\Events\EnvironmentSaved;

Event::listen(EnvironmentSaved::class, function ($event) {
    // Handle environment saved event
});
```

#### `LaravelInstallerFinished`
Fired when installation is completed.

```php
use RachidLaasri\LaravelInstaller\Events\LaravelInstallerFinished;

Event::listen(LaravelInstallerFinished::class, function ($event) {
    // Handle installation finished event
});
```

## Extension Points

### Custom Controllers

Extend package controllers for custom functionality:

```php
namespace App\Http\Controllers\Installer;

use NeuronicLab\Installer\Controllers\EnvironmentController as BaseController;

class CustomEnvironmentController extends BaseController
{
    public function environmentWizard()
    {
        // Add custom logic
        $customData = $this->getCustomData();
        
        $view = parent::environmentWizard();
        return $view->with('customData', $customData);
    }
}
```

### Custom Middleware

Create custom middleware for additional security:

```php
namespace App\Http\Middleware;

use Closure;

class CustomInstallerSecurity
{
    public function handle($request, Closure $next)
    {
        // Custom security logic
        if (!$this->isAuthorized($request)) {
            abort(403);
        }
        
        return $next($request);
    }
}
```

### Custom Views

Override package views by publishing and modifying:

```bash
php artisan neuroniclab:publish --tag=views
```

Then modify files in `resources/views/vendor/neuroniclab-installer/`

## Error Handling

### Exception Classes

The package uses standard Laravel exceptions:

- `ValidationException`: Form validation errors
- `Exception`: General errors during installation
- `PDOException`: Database connection errors

### Error Responses

Controllers return appropriate error responses:

```php
// Validation errors
return redirect()->back()->withErrors($validator->errors());

// General errors
return redirect()->back()->with('error', 'Installation failed');

// Success responses
return redirect()->route('NeuronicLabInstaller::final')->with('success', 'Installation completed');
```

---

**Package**: neuroniclab/installer  
**Version**: 1.0.0  
**Namespace**: NeuronicLab\Installer  
**Last Updated**: January 2024
