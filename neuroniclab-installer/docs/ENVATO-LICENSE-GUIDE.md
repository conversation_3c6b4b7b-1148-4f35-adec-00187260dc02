# Envato License Verification Implementation Guide

## Overview

This guide provides comprehensive instructions for implementing Envato license verification with the NeuronicLab Installer package. It covers both CodeCanyon and ThemeForest license validation using Envato's official API.

## Table of Contents

1. [Envato API Setup](#envato-api-setup)
2. [License Verification Server](#license-verification-server)
3. [Installer Configuration](#installer-configuration)
4. [Implementation Examples](#implementation-examples)
5. [Security Best Practices](#security-best-practices)
6. [Testing and Debugging](#testing-and-debugging)
7. [Error Handling](#error-handling)
8. [Advanced Features](#advanced-features)

## Envato API Setup

### 1. Create Envato API Token

1. **Login to Envato Market**: Go to [build.envato.com/api](https://build.envato.com/api/)
2. **Create New Token**: 
   - Click "Create a Token"
   - Name: "License Verification API"
   - Permissions: Select "View and search Envato sites" and "View the user's purchases"
3. **Save Token**: Copy and securely store your Personal Token

### 2. Get Item Information

```bash
# Get your item ID from the URL
# Example: https://codecanyon.net/item/your-script/12345678
# Item ID: 12345678
```

### 3. Required Information

For license verification, you'll need:
- **Personal Token**: Your Envato API token
- **Item ID**: Your product's Envato item ID
- **Purchase Code**: Customer's purchase code (provided by customer)
- **Buyer Username**: Customer's Envato username (optional)

## License Verification Server

### 1. Create Verification Endpoint

Create a secure endpoint to handle license verification:

```php
<?php
// routes/api.php or routes/web.php

Route::post('/api/verify-license', [LicenseController::class, 'verify'])
    ->middleware(['throttle:10,1']); // Rate limiting
```

### 2. License Verification Controller

```php
<?php
// app/Http/Controllers/LicenseController.php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class LicenseController extends Controller
{
    private $envatoApiUrl = 'https://api.envato.com/v3/market';
    private $personalToken;
    private $itemId;

    public function __construct()
    {
        $this->personalToken = config('license-secure.license.personal_token');
        $this->itemId = config('license-secure.license.item_id');
    }

    public function verify(Request $request)
    {
        $request->validate([
            'purchase_code' => 'required|string',
            'email' => 'required|email',
            'username' => 'required|string',
            'domain' => 'required|url',
        ]);

        try {
            $purchaseCode = $request->input('purchase_code');
            $domain = $request->input('domain');
            $email = $request->input('email');
            $username = $request->input('username');

            // Check cache first to avoid API rate limits
            $cacheKey = 'license_verification_' . md5($purchaseCode);
            if (Cache::has($cacheKey)) {
                $cachedResult = Cache::get($cacheKey);
                if ($cachedResult['valid']) {
                    return $this->successResponse($cachedResult['data']);
                }
            }

            // Verify purchase code with Envato API
            $verificationResult = $this->verifyWithEnvatoAPI($purchaseCode);

            if (!$verificationResult['valid']) {
                return $this->errorResponse($verificationResult['message']);
            }

            $purchaseData = $verificationResult['data'];

            // Additional validations
            $validationResult = $this->validatePurchase($purchaseData, $email, $username, $domain);

            if (!$validationResult['valid']) {
                return $this->errorResponse($validationResult['message']);
            }

            // Cache successful verification for 24 hours
            Cache::put($cacheKey, [
                'valid' => true,
                'data' => $purchaseData
            ], now()->addHours(24));

            // Log successful verification
            Log::info('License verified successfully', [
                'purchase_code' => substr($purchaseCode, 0, 8) . '...',
                'domain' => $domain,
                'item_id' => $this->itemId
            ]);

            return $this->successResponse($purchaseData);

        } catch (\Exception $e) {
            Log::error('License verification failed', [
                'error' => $e->getMessage(),
                'purchase_code' => substr($request->input('purchase_code'), 0, 8) . '...'
            ]);

            return $this->errorResponse('License verification failed. Please try again.');
        }
    }

    private function verifyWithEnvatoAPI($purchaseCode)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->personalToken,
                'User-Agent' => 'License Verification System'
            ])->get($this->envatoApiUrl . '/author/sale', [
                'code' => $purchaseCode
            ]);

            if ($response->status() === 404) {
                return [
                    'valid' => false,
                    'message' => 'Invalid purchase code. Please check and try again.'
                ];
            }

            if (!$response->successful()) {
                return [
                    'valid' => false,
                    'message' => 'Unable to verify license. Please try again later.'
                ];
            }

            $data = $response->json();

            // Check if the purchase is for the correct item
            if ($data['item']['id'] != $this->itemId) {
                return [
                    'valid' => false,
                    'message' => 'This purchase code is not valid for this product.'
                ];
            }

            return [
                'valid' => true,
                'data' => $data
            ];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'message' => 'License verification service unavailable.'
            ];
        }
    }

    private function validatePurchase($purchaseData, $email, $username, $domain)
    {
        // Check if license is already used (optional - implement your own logic)
        if ($this->isLicenseAlreadyUsed($purchaseData['code'], $domain)) {
            return [
                'valid' => false,
                'message' => 'This license is already in use on another domain.'
            ];
        }

        // Validate buyer email (optional)
        if (config('services.envato.validate_buyer_email', false)) {
            if (strtolower($purchaseData['buyer']) !== strtolower($email)) {
                return [
                    'valid' => false,
                    'message' => 'Email address does not match the license buyer.'
                ];
            }
        }

        // Check license type (Regular vs Extended)
        $licenseType = $purchaseData['license'];
        if ($licenseType === 'Regular License') {
            // Add any Regular License specific validations
        } elseif ($licenseType === 'Extended License') {
            // Add any Extended License specific validations
        }

        // Record license usage
        $this->recordLicenseUsage($purchaseData['code'], $domain, $email, $username);

        return ['valid' => true];
    }

    private function isLicenseAlreadyUsed($purchaseCode, $domain)
    {
        // Implement your logic to check if license is already used
        // This could be a database check, file check, or external service
        
        // Example database check:
        // return DB::table('license_usage')
        //     ->where('purchase_code', $purchaseCode)
        //     ->where('domain', '!=', $domain)
        //     ->exists();
        
        return false; // For now, allow multiple uses
    }

    private function recordLicenseUsage($purchaseCode, $domain, $email, $username)
    {
        // Record license usage in your system
        // This could be database, file, or external service
        
        // Example database record:
        // DB::table('license_usage')->updateOrInsert(
        //     ['purchase_code' => $purchaseCode, 'domain' => $domain],
        //     [
        //         'email' => $email,
        //         'username' => $username,
        //         'verified_at' => now(),
        //         'updated_at' => now()
        //     ]
        // );
    }

    private function successResponse($data)
    {
        return response()->json([
            'status' => 'success',
            'message' => 'License verified successfully',
            'data' => [
                'item_name' => $data['item']['name'],
                'license_type' => $data['license'],
                'purchase_date' => $data['sold_at'],
                'buyer' => $data['buyer'],
                'valid' => true
            ]
        ]);
    }

    private function errorResponse($message)
    {
        return response()->json([
            'status' => 'error',
            'message' => $message,
            'valid' => false
        ], 400);
    }
}
```

### 3. Secure Configuration Setup

Create a secure configuration file `config/license-secure.php`:

```php
<?php
// config/license-secure.php

return [
    'license' => [
        // Store encrypted token in database or secure file
        'personal_token' => app('encrypter')->decrypt(file_get_contents(storage_path('app/secure/license_token.enc'))),
        'item_id' => '12345678', // Your actual item ID
        'validate_buyer_email' => false,
        'allow_multiple_domains' => false,
        'api_url' => 'https://api.envato.com/v3/market',
        'timeout' => 30,
    ],
];
```

Create secure token storage:

```bash
# Create secure directory
mkdir -p storage/app/secure
chmod 700 storage/app/secure

# Create encrypted token file (run this once)
php artisan tinker
>>> $token = 'your_envato_personal_token_here';
>>> $encrypted = app('encrypter')->encrypt($token);
>>> file_put_contents(storage_path('app/secure/license_token.enc'), $encrypted);
>>> chmod(storage_path('app/secure/license_token.enc'), 0600);
>>> exit
```

Alternative secure method using database:

```php
// Create migration for secure config
php artisan make:migration create_secure_configs_table

// Migration content:
Schema::create('secure_configs', function (Blueprint $table) {
    $table->id();
    $table->string('key')->unique();
    $table->text('value'); // Encrypted value
    $table->timestamps();
});

// Store token securely in database
DB::table('secure_configs')->insert([
    'key' => 'license_personal_token',
    'value' => encrypt('your_envato_personal_token_here'),
    'created_at' => now(),
    'updated_at' => now(),
]);

// Retrieve in config:
'personal_token' => decrypt(DB::table('secure_configs')->where('key', 'license_personal_token')->value('value')),
```

## Installer Configuration

### 1. Configure NeuronicLab Installer

Update your `config/neuroniclab-installer.php`:

```php
<?php
// config/neuroniclab-installer.php

return [
    // ... other configuration

    'license' => [
        'enabled' => true,
        'verification_file' => 'storage/app/secure/license_verified',
        'verification_url' => request()->getSchemeAndHttpHost() . '/api/verify-license',
        'item_id' => config('license-secure.license.item_id'),
        'item_name' => 'Your Product Name',
        'required_fields' => [
            'email' => true,
            'username' => true,
            'purchase_code' => true,
        ],
        'envato' => [
            'marketplace' => 'codecanyon', // or 'themeforest'
            'author_username' => 'your_envato_username',
            'item_url' => 'https://codecanyon.net/item/your-item/12345678',
        ],
    ],

    // ... rest of configuration
];
```

### 2. Custom License View

Create a custom license view for Envato-specific information:

```bash
php artisan neuroniclab:publish --tag=views
```

Edit `resources/views/vendor/neuroniclab-installer/license.blade.php`:

```blade
@extends('neuroniclab-installer::layouts.master')

@section('template_title')
    {{ trans('neuroniclab-installer::installer_messages.license.templateTitle') }}
@endsection

@section('title')
    <i class="fa fa-key fa-fw" aria-hidden="true"></i>
    Envato License Verification
@endsection

@section('container')
    <div class="tabs tabs-full">
        <form method="post" action="{{ route('NeuronicLabInstaller::licenseCheck') }}" class="tabs-wrap">
            @csrf

            {{-- Envato Information Panel --}}
            <div class="alert alert-info">
                <h4><i class="fa fa-info-circle"></i> Envato License Information</h4>
                <p><strong>Product:</strong> {{ config('neuroniclab-installer.license.item_name') }}</p>
                <p><strong>Marketplace:</strong> {{ ucfirst(config('neuroniclab-installer.license.envato.marketplace')) }}</p>
                <p><strong>Author:</strong> {{ config('neuroniclab-installer.license.envato.author_username') }}</p>
                @if(config('neuroniclab-installer.license.envato.item_url'))
                    <p><strong>Product URL:</strong> 
                        <a href="{{ config('neuroniclab-installer.license.envato.item_url') }}" target="_blank">
                            View on Envato Market
                        </a>
                    </p>
                @endif
            </div>

            {{-- Error Messages --}}
            @if(session()->has('license_error'))
                <div class="alert alert-danger">
                    <button type="button" class="close" onclick="this.parentElement.style.display='none'">
                        <i class="fa fa-close"></i>
                    </button>
                    <p style="margin-bottom: 0px;">{{ session()->get('license_error') }}</p>
                </div>
            @endif

            {{-- Success Messages --}}
            @if(session()->has('license_success'))
                <div class="alert alert-success">
                    <button type="button" class="close" onclick="this.parentElement.style.display='none'">
                        <i class="fa fa-close"></i>
                    </button>
                    <p style="margin-bottom: 0px;">{{ session()->get('license_success') }}</p>
                </div>
            @endif

            {{-- License Form --}}
            <div class="form-group {{ $errors->has('purchase_code') ? 'has-error' : '' }}">
                <label for="purchase_code">
                    <i class="fa fa-key"></i> Envato Purchase Code *
                </label>
                <input type="text" 
                       name="purchase_code" 
                       id="purchase_code" 
                       value="{{ old('purchase_code') }}" 
                       placeholder="Enter your Envato purchase code"
                       class="form-control" />
                <small class="help-text">
                    <a href="https://help.market.envato.com/hc/en-us/articles/202822600-Where-Is-My-Purchase-Code-" target="_blank">
                        <i class="fa fa-question-circle"></i> Where to find your purchase code?
                    </a>
                </small>
                @if ($errors->has('purchase_code'))
                    <span class="error-block">
                        <i class="fa fa-fw fa-exclamation-triangle"></i>
                        {{ $errors->first('purchase_code') }}
                    </span>
                @endif
            </div>

            <div class="form-group {{ $errors->has('email') ? 'has-error' : '' }}">
                <label for="email">
                    <i class="fa fa-envelope"></i> Email Address *
                </label>
                <input type="email" 
                       name="email" 
                       id="email" 
                       value="{{ old('email') }}" 
                       placeholder="Your email address"
                       class="form-control" />
                <small class="help-text">Use the email associated with your Envato account</small>
                @if ($errors->has('email'))
                    <span class="error-block">
                        <i class="fa fa-fw fa-exclamation-triangle"></i>
                        {{ $errors->first('email') }}
                    </span>
                @endif
            </div>

            <div class="form-group {{ $errors->has('username') ? 'has-error' : '' }}">
                <label for="username">
                    <i class="fa fa-user"></i> Envato Username *
                </label>
                <input type="text" 
                       name="username" 
                       id="username" 
                       value="{{ old('username') }}" 
                       placeholder="Your Envato username"
                       class="form-control" />
                <small class="help-text">Your username on CodeCanyon/ThemeForest</small>
                @if ($errors->has('username'))
                    <span class="error-block">
                        <i class="fa fa-fw fa-exclamation-triangle"></i>
                        {{ $errors->first('username') }}
                    </span>
                @endif
            </div>

            {{-- License Types Information --}}
            <div class="alert alert-warning">
                <h5><i class="fa fa-info-circle"></i> License Types</h5>
                <ul style="margin-bottom: 0;">
                    <li><strong>Regular License:</strong> For single end product (website/application)</li>
                    <li><strong>Extended License:</strong> For multiple end products or SaaS applications</li>
                </ul>
                <p style="margin-top: 10px; margin-bottom: 0;">
                    <small>Make sure you have the appropriate license for your intended use.</small>
                </p>
            </div>

            <div class="buttons">
                <button class="button" type="submit">
                    <i class="fa fa-check"></i> Verify Envato License
                </button>
            </div>
        </form>
    </div>

    {{-- Additional Styling --}}
    <style>
        .help-text {
            display: block;
            margin-top: 5px;
            color: #666;
            font-size: 12px;
        }
        .help-text a {
            color: #007bff;
            text-decoration: none;
        }
        .help-text a:hover {
            text-decoration: underline;
        }
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-info {
            color: #31708f;
            background-color: #d9edf7;
            border-color: #bce8f1;
        }
        .alert-warning {
            color: #8a6d3b;
            background-color: #fcf8e3;
            border-color: #faebcc;
        }
        .alert-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
        .alert-success {
            color: #3c763d;
            background-color: #dff0d8;
            border-color: #d6e9c6;
        }
        .close {
            float: right;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
        }
        .error-block {
            color: #a94442;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }
    </style>
@endsection
```

## Implementation Examples

### 1. Database Migration for License Tracking

Create a migration to track license usage:

```bash
php artisan make:migration create_license_verifications_table
```

```php
<?php
// database/migrations/xxxx_xx_xx_create_license_verifications_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLicenseVerificationsTable extends Migration
{
    public function up()
    {
        Schema::create('license_verifications', function (Blueprint $table) {
            $table->id();
            $table->string('purchase_code')->unique();
            $table->string('domain');
            $table->string('email');
            $table->string('username');
            $table->string('buyer_name')->nullable();
            $table->string('license_type'); // Regular or Extended
            $table->string('item_id');
            $table->string('item_name');
            $table->timestamp('purchase_date')->nullable();
            $table->timestamp('verified_at');
            $table->json('verification_data')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['purchase_code', 'domain']);
            $table->index('is_active');
        });
    }

    public function down()
    {
        Schema::dropIfExists('license_verifications');
    }
}
```

### 2. License Model

```php
<?php
// app/Models/LicenseVerification.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class LicenseVerification extends Model
{
    protected $fillable = [
        'purchase_code',
        'domain',
        'email',
        'username',
        'buyer_name',
        'license_type',
        'item_id',
        'item_name',
        'purchase_date',
        'verified_at',
        'verification_data',
        'is_active'
    ];

    protected $casts = [
        'purchase_date' => 'datetime',
        'verified_at' => 'datetime',
        'verification_data' => 'array',
        'is_active' => 'boolean'
    ];

    public function isExpired()
    {
        // Implement your license expiration logic
        // For example, if licenses expire after 1 year:
        return $this->verified_at->addYear()->isPast();
    }

    public function isValidForDomain($domain)
    {
        return $this->domain === $domain && $this->is_active && !$this->isExpired();
    }

    public static function findByPurchaseCode($purchaseCode)
    {
        return static::where('purchase_code', $purchaseCode)->first();
    }

    public static function isCodeUsedOnDifferentDomain($purchaseCode, $domain)
    {
        return static::where('purchase_code', $purchaseCode)
            ->where('domain', '!=', $domain)
            ->where('is_active', true)
            ->exists();
    }
}
```

### 3. Enhanced License Controller with Database

Update the license controller to use the database:

```php
<?php
// Update the EnvatoLicenseController methods

private function isLicenseAlreadyUsed($purchaseCode, $domain)
{
    return EnvatoLicense::isCodeUsedOnDifferentDomain($purchaseCode, $domain);
}

private function recordLicenseUsage($purchaseCode, $domain, $email, $username)
{
    $existingLicense = EnvatoLicense::findByPurchaseCode($purchaseCode);

    if ($existingLicense) {
        // Update existing record
        $existingLicense->update([
            'domain' => $domain,
            'email' => $email,
            'username' => $username,
            'verified_at' => now(),
            'is_active' => true
        ]);
    } else {
        // Create new record
        EnvatoLicense::create([
            'purchase_code' => $purchaseCode,
            'domain' => $domain,
            'email' => $email,
            'username' => $username,
            'verified_at' => now(),
            'is_active' => true
        ]);
    }
}
```

## Security Best Practices

### 1. Secure Token Management

**Never store tokens in plain text or .env files:**

```php
// Method 1: Encrypted file storage
class SecureTokenManager
{
    public static function getEnvatoToken()
    {
        $encryptedPath = storage_path('app/secure/envato_token.enc');

        if (!file_exists($encryptedPath)) {
            throw new Exception('Envato token not found');
        }

        return decrypt(file_get_contents($encryptedPath));
    }

    public static function setEnvatoToken($token)
    {
        $securePath = storage_path('app/secure');

        if (!is_dir($securePath)) {
            mkdir($securePath, 0700, true);
        }

        $encryptedToken = encrypt($token);
        file_put_contents($securePath . '/envato_token.enc', $encryptedToken);
        chmod($securePath . '/envato_token.enc', 0600);
    }
}

// Method 2: Database with encryption
class EnvatoConfig extends Model
{
    protected $fillable = ['key', 'value'];
    protected $casts = ['value' => 'encrypted'];

    public static function getToken()
    {
        return static::where('key', 'personal_token')->value('value');
    }
}
```

### 2. Advanced Rate Limiting

Implement multi-layer rate limiting:

```php
// routes/api.php
Route::post('/api/verify-envato-license', [EnvatoLicenseController::class, 'verify'])
    ->middleware([
        'throttle:5,1',        // 5 per minute per IP
        'throttle:20,60',      // 20 per hour per IP
        'throttle:100,1440'    // 100 per day per IP
    ]);

// Custom rate limiting middleware
class EnvatoRateLimit
{
    public function handle($request, Closure $next)
    {
        $key = 'envato_verify_' . $request->ip();
        $attempts = Cache::get($key, 0);

        if ($attempts >= 5) {
            // Log suspicious activity
            Log::warning('Excessive license verification attempts', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'attempts' => $attempts
            ]);

            abort(429, 'Too many verification attempts');
        }

        Cache::put($key, $attempts + 1, now()->addMinutes(5));

        return $next($request);
    }
}
```

### 3. Request Validation and Sanitization

Implement strict input validation:

```php
class EnvatoLicenseRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'purchase_code' => [
                'required',
                'string',
                'regex:/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i',
                'max:36'
            ],
            'email' => 'required|email|max:255',
            'username' => 'required|string|max:100|regex:/^[a-zA-Z0-9_-]+$/',
            'domain' => 'required|url|max:255'
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'purchase_code' => strtolower(trim($this->purchase_code)),
            'email' => strtolower(trim($this->email)),
            'username' => trim($this->username),
            'domain' => rtrim(strtolower(trim($this->domain)), '/')
        ]);
    }
}
```

### 4. HTTPS and Security Headers

Force HTTPS and add security headers:

```php
// app/Http/Middleware/SecurityHeaders.php
class SecurityHeaders
{
    public function handle($request, Closure $next)
    {
        // Force HTTPS in production
        if (!$request->secure() && app()->environment('production')) {
            return redirect()->secure($request->getRequestUri(), 301);
        }

        $response = $next($request);

        // Add security headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        $response->headers->set('Content-Security-Policy', "default-src 'self'");

        return $response;
    }
}
```

### 5. Audit Logging and Monitoring

Implement comprehensive logging:

```php
class SecurityLogger
{
    public static function logVerificationAttempt($request, $result, $purchaseCode = null)
    {
        $logData = [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'domain' => $request->input('domain'),
            'email' => $request->input('email'),
            'username' => $request->input('username'),
            'result' => $result['status'],
            'timestamp' => now(),
            'purchase_code_hash' => $purchaseCode ? hash('sha256', $purchaseCode) : null,
        ];

        // Log to separate security log
        Log::channel('security')->info('License verification attempt', $logData);

        // Store in database for analysis
        DB::table('security_logs')->insert($logData);

        // Alert on suspicious patterns
        if (!$result['valid']) {
            static::checkSuspiciousActivity($request->ip());
        }
    }

    private static function checkSuspiciousActivity($ip)
    {
        $recentFailures = DB::table('security_logs')
            ->where('ip', $ip)
            ->where('result', 'error')
            ->where('timestamp', '>', now()->subHour())
            ->count();

        if ($recentFailures >= 10) {
            // Alert administrators
            Log::channel('alerts')->critical('Suspicious license verification activity', [
                'ip' => $ip,
                'failures' => $recentFailures
            ]);
        }
    }
}
```

## Testing and Debugging

### 1. Test License Verification

Create a test command to verify your implementation:

```php
<?php
// app/Console/Commands/TestEnvatoLicense.php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\EnvatoLicenseController;
use Illuminate\Http\Request;

class TestEnvatoLicense extends Command
{
    protected $signature = 'envato:test-license {purchase_code} {email} {username} {domain}';
    protected $description = 'Test Envato license verification';

    public function handle()
    {
        $controller = new EnvatoLicenseController();
        
        $request = new Request([
            'purchase_code' => $this->argument('purchase_code'),
            'email' => $this->argument('email'),
            'username' => $this->argument('username'),
            'domain' => $this->argument('domain'),
        ]);

        $response = $controller->verify($request);
        
        $this->info('Response: ' . $response->getContent());
    }
}
```

Usage:
```bash
php artisan envato:test-license "your-purchase-code" "<EMAIL>" "buyer_username" "https://yourdomain.com"
```

### 2. Debug Mode

Add debug logging for troubleshooting:

```php
// In your .env file for development
LOG_LEVEL=debug
ENVATO_DEBUG=true

// In your controller
if (config('app.debug') && env('ENVATO_DEBUG')) {
    Log::debug('Envato API Request', [
        'url' => $this->envatoApiUrl . '/author/sale',
        'purchase_code' => substr($purchaseCode, 0, 8) . '...',
        'headers' => ['Authorization' => 'Bearer ' . substr($this->personalToken, 0, 10) . '...']
    ]);
}
```

### 3. Mock Testing

For testing without hitting the Envato API:

```php
// tests/Feature/EnvatoLicenseTest.php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Support\Facades\Http;

class EnvatoLicenseTest extends TestCase
{
    public function test_valid_license_verification()
    {
        Http::fake([
            'api.envato.com/*' => Http::response([
                'item' => ['id' => '12345678', 'name' => 'Test Item'],
                'license' => 'Regular License',
                'sold_at' => '2024-01-01T00:00:00Z',
                'buyer' => 'testbuyer',
                'code' => 'test-purchase-code'
            ], 200)
        ]);

        $response = $this->postJson('/api/verify-envato-license', [
            'purchase_code' => 'test-purchase-code',
            'email' => '<EMAIL>',
            'username' => 'testuser',
            'domain' => 'https://example.com'
        ]);

        $response->assertStatus(200)
                ->assertJson(['status' => 'success']);
    }

    public function test_invalid_license_verification()
    {
        Http::fake([
            'api.envato.com/*' => Http::response([], 404)
        ]);

        $response = $this->postJson('/api/verify-envato-license', [
            'purchase_code' => 'invalid-code',
            'email' => '<EMAIL>',
            'username' => 'testuser',
            'domain' => 'https://example.com'
        ]);

        $response->assertStatus(400)
                ->assertJson(['status' => 'error']);
    }
}
```

## Error Handling

### 1. Common Error Scenarios

Handle various error scenarios gracefully:

```php
// In your EnvatoLicenseController

private function handleEnvatoAPIErrors($response)
{
    switch ($response->status()) {
        case 401:
            return [
                'valid' => false,
                'message' => 'License verification service authentication failed.'
            ];
        
        case 403:
            return [
                'valid' => false,
                'message' => 'License verification service access denied.'
            ];
        
        case 404:
            return [
                'valid' => false,
                'message' => 'Invalid purchase code. Please check and try again.'
            ];
        
        case 429:
            return [
                'valid' => false,
                'message' => 'Too many verification attempts. Please try again later.'
            ];
        
        case 500:
        case 502:
        case 503:
            return [
                'valid' => false,
                'message' => 'License verification service temporarily unavailable.'
            ];
        
        default:
            return [
                'valid' => false,
                'message' => 'License verification failed. Please contact support.'
            ];
    }
}
```

### 2. Fallback Mechanisms

Implement fallback for when Envato API is unavailable:

```php
private function verifyWithFallback($purchaseCode)
{
    // Try primary verification
    $result = $this->verifyWithEnvatoAPI($purchaseCode);
    
    if (!$result['valid'] && $this->isAPIUnavailable($result)) {
        // Check local cache or database for previously verified licenses
        $cachedLicense = $this->getCachedLicense($purchaseCode);
        
        if ($cachedLicense && $this->isCacheValid($cachedLicense)) {
            Log::warning('Using cached license verification due to API unavailability', [
                'purchase_code' => substr($purchaseCode, 0, 8) . '...'
            ]);
            
            return [
                'valid' => true,
                'data' => $cachedLicense,
                'source' => 'cache'
            ];
        }
    }
    
    return $result;
}
```

## Advanced Features

### 1. License Renewal Notifications

Implement license expiration notifications:

```php
// app/Console/Commands/CheckLicenseExpiration.php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EnvatoLicense;
use Illuminate\Support\Facades\Mail;

class CheckLicenseExpiration extends Command
{
    protected $signature = 'envato:check-expiration';
    protected $description = 'Check for expiring licenses and send notifications';

    public function handle()
    {
        $expiringLicenses = EnvatoLicense::where('is_active', true)
            ->where('verified_at', '<', now()->subMonths(11)) // 11 months old
            ->where('verified_at', '>', now()->subYear()) // Less than 1 year old
            ->get();

        foreach ($expiringLicenses as $license) {
            // Send expiration warning email
            Mail::to($license->email)->send(new LicenseExpirationWarning($license));
            
            $this->info("Expiration warning sent for license: " . substr($license->purchase_code, 0, 8) . '...');
        }
    }
}
```

### 2. License Analytics

Track license usage analytics:

```php
// app/Models/LicenseAnalytics.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LicenseAnalytics extends Model
{
    protected $fillable = [
        'purchase_code',
        'domain',
        'action', // 'verified', 'accessed', 'renewed'
        'ip_address',
        'user_agent',
        'metadata'
    ];

    protected $casts = [
        'metadata' => 'array'
    ];

    public static function logVerification($purchaseCode, $domain, $request)
    {
        static::create([
            'purchase_code' => $purchaseCode,
            'domain' => $domain,
            'action' => 'verified',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'metadata' => [
                'timestamp' => now(),
                'success' => true
            ]
        ]);
    }
}
```

### 3. Multi-Domain License Support

For Extended License holders:

```php
private function validateMultiDomainLicense($purchaseData, $domain)
{
    if ($purchaseData['license'] === 'Extended License') {
        // Allow multiple domains for Extended License
        $existingDomains = EnvatoLicense::where('purchase_code', $purchaseData['code'])
            ->pluck('domain')
            ->toArray();
        
        $maxDomains = config('services.envato.max_domains_extended', 5);
        
        if (count($existingDomains) >= $maxDomains && !in_array($domain, $existingDomains)) {
            return [
                'valid' => false,
                'message' => "Extended License allows maximum {$maxDomains} domains."
            ];
        }
    }
    
    return ['valid' => true];
}
```

---

This comprehensive guide provides everything needed to implement Envato license verification with the NeuronicLab Installer package. The implementation includes security best practices, error handling, testing procedures, and advanced features for production use.
