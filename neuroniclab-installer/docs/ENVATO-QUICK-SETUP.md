# Envato License Integration - Secure Quick Setup Guide

## 🔒 Secure Setup (10 Minutes)

### Step 1: Get Your Envato Credentials

1. **Get Personal Token**:
   - Go to [build.envato.com/api](https://build.envato.com/api/)
   - Click "Create a Token"
   - Name: "License Verification"
   - Permissions: ✅ "View and search Envato sites" + ✅ "View the user's purchases"
   - Copy your token (keep it secure!)

2. **Get Item ID**:
   - From your item URL: `https://codecanyon.net/item/your-script/12345678`
   - Item ID is: `12345678`

### Step 2: Secure Configuration Setup

**Create secure configuration file** `config/license-secure.php`:

```php
<?php
// config/license-secure.php

return [
    'license' => [
        'personal_token' => app('encrypter')->decrypt(file_get_contents(storage_path('app/secure/license_token.enc'))),
        'item_id' => '12345678', // Your actual item ID
        'item_name' => 'Your Product Name',
        'marketplace' => 'codecanyon',
        'author_username' => 'your_username',
        'api_url' => 'https://api.envato.com/v3/market',
        'timeout' => 30,
        'validate_buyer_email' => false,
        'allow_multiple_domains' => false,
    ],
];
```

**Store token securely**:

```bash
# Create secure directory
mkdir -p storage/app/secure
chmod 700 storage/app/secure

# Store encrypted token (run once)
php artisan tinker
>>> $token = 'your_envato_personal_token_here';
>>> $encrypted = app('encrypter')->encrypt($token);
>>> file_put_contents(storage_path('app/secure/license_token.enc'), $encrypted);
>>> chmod(storage_path('app/secure/license_token.enc'), 0600);
>>> exit
```

### Step 3: Update Installer Configuration

Edit `config/neuroniclab-installer.php`:

```php
'license' => [
    'enabled' => true,
    'verification_file' => 'storage/app/secure/license_verified',
    'verification_url' => request()->getSchemeAndHttpHost() . '/api/verify-license',
    'item_id' => config('license-secure.license.item_id'),
    'item_name' => config('license-secure.license.item_name'),
    'required_fields' => [
        'email' => true,
        'username' => true,
        'purchase_code' => true,
    ],
],
```

### Step 4: Add Secure Verification Route

Add to `routes/api.php` or `routes/web.php`:

```php
Route::post('/api/verify-license', [LicenseController::class, 'verify'])
    ->middleware([
        'throttle:5,1',        // 5 per minute
        'throttle:20,60',      // 20 per hour
        'throttle:100,1440'    // 100 per day
    ]);
```

### Step 5: Create Secure Controller

```bash
php artisan make:controller LicenseController
```

**Update constructor to use secure config**:

```php
public function __construct()
{
    $this->personalToken = config('license-secure.license.personal_token');
    $this->itemId = config('license-secure.license.item_id');
}
```

Copy the full controller code from `ENVATO-LICENSE-GUIDE.md` (Section: License Verification Controller).

### Step 6: Add Security Middleware

Create security middleware:

```bash
php artisan make:middleware SecurityHeaders
```

```php
// app/Http/Middleware/SecurityHeaders.php
public function handle($request, Closure $next)
{
    if (!$request->secure() && app()->environment('production')) {
        return redirect()->secure($request->getRequestUri(), 301);
    }

    $response = $next($request);
    $response->headers->set('X-Content-Type-Options', 'nosniff');
    $response->headers->set('X-Frame-Options', 'DENY');
    $response->headers->set('Strict-Transport-Security', 'max-age=31536000');

    return $response;
}
```

Register in `app/Http/Kernel.php`:

```php
protected $middleware = [
    // ... other middleware
    \App\Http\Middleware\SecurityHeaders::class,
];
```

### Step 7: Test Installation

```bash
# Test the installer
php artisan serve
# Visit: https://localhost:8000/install (use HTTPS in production)
```

## 🔧 Advanced Secure Setup (Optional)

### Database Tracking with Encryption

```bash
# Create migration
php artisan make:migration create_license_verifications_table

# Add encrypted columns in migration:
$table->text('purchase_code_hash'); // Store hash, not actual code
$table->text('verification_data')->nullable(); // Encrypted JSON data
```

### Secure Custom Views

```bash
# Publish views
php artisan neuroniclab:publish --tag=views

# Edit with security considerations:
# resources/views/vendor/neuroniclab-installer/license.blade.php
```

### Secure Testing Command

```bash
# Create test command with security
php artisan make:command TestLicense

# Test with hashed data
php artisan license:test "purchase-code" "<EMAIL>" "username" "https://domain.com"
```

## 📋 Security Verification Checklist

- [ ] Envato Personal Token encrypted and stored securely
- [ ] No sensitive data in .env files
- [ ] Item ID matches your product
- [ ] Verification endpoint responds with HTTPS
- [ ] License form displays correctly
- [ ] Valid license verification works
- [ ] Invalid license shows error
- [ ] Multi-layer rate limiting is active
- [ ] Security headers are set
- [ ] Audit logging is working
- [ ] File permissions are restrictive (700/600)
- [ ] Database encryption is enabled

## 🐛 Secure Troubleshooting

### License Verification Fails

```bash
# Check secure configuration
php artisan tinker
>>> config('license-secure.license.personal_token') // Should show decrypted token
>>> config('license-secure.license.item_id')

# Test API connectivity (use your actual token)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.envato.com/v3/market/author/sale?code=PURCHASE_CODE"

# Check file permissions
ls -la storage/app/secure/
# Should show: -rw------- (600) for license_token.enc
```

### Common Issues

| Issue | Solution |
|-------|----------|
| 401 Unauthorized | Check your personal token |
| 404 Not Found | Verify purchase code and item ID |
| 429 Rate Limited | Enable caching, reduce requests |
| 500 Server Error | Check Envato API status |

### Secure Debug Mode

```php
// Enable debugging in config/license-secure.php
'debug_mode' => app()->environment('local'),
'log_level' => 'debug',

// Check secure logs
tail -f storage/logs/laravel.log
tail -f storage/logs/security.log
```

## 🔒 Enhanced Security Checklist

- [ ] Personal token is encrypted (never in plain text)
- [ ] No sensitive data in .env or version control
- [ ] Multi-layer rate limiting is enabled
- [ ] HTTPS is enforced in production
- [ ] Security headers are set
- [ ] Input validation and sanitization active
- [ ] Error messages don't expose sensitive data
- [ ] Logs contain hashed data only (no full purchase codes)
- [ ] File permissions are restrictive (700/600)
- [ ] Database encryption is enabled
- [ ] Audit logging is comprehensive
- [ ] Suspicious activity monitoring is active

## 📚 Additional Resources

- **Full Documentation**: `ENVATO-LICENSE-GUIDE.md`
- **Configuration Reference**: `config/envato-integration.php`
- **Environment Template**: `.env.envato.example`
- **API Documentation**: `API-DOCUMENTATION.md`

## 🆘 Support

If you encounter issues:

1. Check the full `ENVATO-LICENSE-GUIDE.md`
2. Review Laravel logs: `storage/logs/laravel.log`
3. Test with debug mode enabled
4. Verify Envato API status
5. Check your token permissions

---

**Time to Complete**: ~10-20 minutes
**Difficulty**: Intermediate to Advanced
**Security Level**: Enterprise-grade
**Requirements**: Envato account, Laravel application, NeuronicLab Installer, Basic security knowledge
