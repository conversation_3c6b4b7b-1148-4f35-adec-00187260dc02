# Integration Guide

This guide explains how to integrate the NeuronicLab Installer package into your Laravel application.

## Prerequisites

- Laravel 10.0 or higher
- PHP 8.1 or higher
- MySQL/MariaDB database

## Step-by-Step Integration

### 1. Install the Package

```bash
composer require neuroniclab/installer
```

### 2. Install Package Assets

```bash
php artisan neuroniclab:install
```

This command will publish:
- Configuration file to `config/neuroniclab-installer.php`
- Views to `resources/views/vendor/neuroniclab-installer/`
- Assets to `public/neuroniclab-installer/`
- Database schema to `database/neuroniclab-installer/`
- Language files to `resources/lang/vendor/neuroniclab-installer/`

### 3. Configure Your Application

#### Update AppServiceProvider

Add installation checking to your `app/Providers/AppServiceProvider.php`:

```php
public function boot()
{
    // Only check installation for HTTP requests, not console commands
    if (!app()->runningInConsole() && 
        !file_exists(storage_path('installed')) && 
        !request()->is('install') && 
        !request()->is('install/*')) {
        header("Location: install/");
        exit;
    }
}
```

#### Register Middleware (Optional)

If you want to use the package middleware, add them to your `app/Http/Kernel.php`:

```php
protected $middlewareAliases = [
    // ... existing middleware
    'neuroniclab-install' => \NeuronicLab\Installer\Middleware\CanInstall::class,
    'neuroniclab-security' => \NeuronicLab\Installer\Middleware\Security::class,
];
```

### 4. Configure the Installer

Edit `config/neuroniclab-installer.php` to customize:

#### Database Schema
```php
'database' => [
    'schema_file' => 'database/neuroniclab-installer/schema.sql',
    'mysql_path' => '/usr/bin/mysql', // Adjust path as needed
],
```

#### License Settings
```php
'license' => [
    'enabled' => true, // Set to false to disable license verification
    'item_name' => 'Your Application Name',
    'verification_url' => 'https://your-domain.com/api/verify-license',
],
```

#### UI Customization
```php
'ui' => [
    'app_name' => 'Your Application',
    'logo' => 'path/to/your/logo.png',
    'primary_color' => '#your-brand-color',
],
```

### 5. Prepare Your Database Schema

1. Export your application's database structure to SQL
2. Place the SQL file at the configured schema file location
3. Ensure the file includes all necessary tables and data

Example:
```bash
mysqldump -u username -p --no-data your_database > database/neuroniclab-installer/schema.sql
```

### 6. Customize Views (Optional)

If you want to customize the installer appearance:

```bash
php artisan neuroniclab:publish --tag=views
```

Then edit the views in `resources/views/vendor/neuroniclab-installer/`

### 7. Test the Installation

1. Access your application at `/install`
2. Follow the installation steps
3. Verify that the installation completes successfully

## Advanced Configuration

### Custom Controllers

You can extend the package controllers for custom functionality:

```php
<?php

namespace App\Http\Controllers\Installer;

use NeuronicLab\Installer\Controllers\EnvironmentController as BaseController;

class EnvironmentController extends BaseController
{
    // Override methods as needed
    public function environmentWizard()
    {
        // Custom logic
        return parent::environmentWizard();
    }
}
```

### Custom Validation Rules

Add custom validation rules in the configuration:

```php
'environment' => [
    'form' => [
        'rules' => [
            'app_name' => 'required|string|max:50',
            'custom_field' => 'required|string',
            // Add your custom rules
        ],
    ],
],
```

### Custom License Verification

Implement custom license verification by creating a verification endpoint:

```php
// routes/api.php
Route::post('/verify-license', function (Request $request) {
    // Your license verification logic
    return response()->json(['status' => 'success']);
});
```

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure web server has write permissions to storage and bootstrap/cache directories
2. **Database Import Fails**: Check MySQL path configuration and file permissions
3. **Assets Not Loading**: Run `php artisan neuroniclab:publish --tag=assets --force`

### Debug Mode

Enable debug mode in your `.env` file during development:
```
APP_DEBUG=true
```

## Security Considerations

1. Remove or secure the installer after deployment
2. Use HTTPS for license verification
3. Validate all user inputs
4. Implement proper error handling

## Support

For issues and questions:
1. Check the configuration file
2. Review the logs in `storage/logs/`
3. Ensure all requirements are met
4. Test with a fresh Laravel installation
