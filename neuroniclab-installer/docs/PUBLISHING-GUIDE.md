# NeuronicLab Installer - Composer Package Publishing Guide

## Table of Contents

1. [Overview](#overview)
2. [Pre-Publishing Checklist](#pre-publishing-checklist)
3. [Package Preparation](#package-preparation)
4. [Version Management](#version-management)
5. [Publishing to Packagist](#publishing-to-packagist)
6. [Private Repository Setup](#private-repository-setup)
7. [Documentation and Marketing](#documentation-and-marketing)
8. [Maintenance and Updates](#maintenance-and-updates)
9. [Quality Assurance](#quality-assurance)
10. [Distribution Strategies](#distribution-strategies)

## Overview

This guide provides comprehensive instructions for publishing the NeuronicLab Installer package to various Composer repositories, including Packagist (public) and private repositories.

### Package Information
- **Name**: `neuroniclab/installer`
- **Type**: Laravel Package
- **License**: MIT
- **Namespace**: `NeuronicLab\Installer`

## Pre-Publishing Checklist

### ✅ Code Quality
- [ ] All code follows PSR-12 coding standards
- [ ] No syntax errors or warnings
- [ ] All classes properly namespaced
- [ ] Documentation comments present
- [ ] No hardcoded values or credentials

### ✅ Testing
- [ ] Package verification script passes
- [ ] Tested with clean Laravel installation
- [ ] All installation steps work correctly
- [ ] Browser testing completed
- [ ] Command-line tools functional

### ✅ Documentation
- [ ] README.md is comprehensive and up-to-date
- [ ] USER-GUIDE.md provides detailed instructions
- [ ] INTEGRATION.md explains implementation
- [ ] CHANGELOG.md documents all changes
- [ ] LICENSE file is present and correct

### ✅ Package Structure
- [ ] composer.json is properly configured
- [ ] All required files are present
- [ ] Assets are organized correctly
- [ ] Configuration files are complete
- [ ] Database schema is valid

### ✅ Security
- [ ] No sensitive information in code
- [ ] Secure default configurations
- [ ] Input validation implemented
- [ ] Error handling in place

## Package Preparation

### 1. Finalize composer.json

Ensure your `composer.json` is production-ready:

```json
{
    "name": "neuroniclab/installer",
    "description": "A comprehensive Laravel installer package with custom UI, license verification, and database setup",
    "type": "library",
    "license": "MIT",
    "keywords": ["laravel", "installer", "package", "setup", "deployment"],
    "homepage": "https://github.com/neuroniclab/installer",
    "authors": [
        {
            "name": "NeuronicLab Team",
            "email": "<EMAIL>",
            "homepage": "https://neuroniclab.com"
        }
    ],
    "require": {
        "php": "^8.1",
        "laravel/framework": "^10.0",
        "nesbot/carbon": "^2.0",
        "guzzlehttp/guzzle": "^7.0"
    },
    "require-dev": {
        "phpunit/phpunit": "^10.0",
        "orchestra/testbench": "^8.0"
    },
    "autoload": {
        "psr-4": {
            "NeuronicLab\\Installer\\": "src/"
        },
        "files": [
            "src/Helpers/functions.php"
        ]
    },
    "autoload-dev": {
        "psr-4": {
            "NeuronicLab\\Installer\\Tests\\": "tests/"
        }
    },
    "extra": {
        "laravel": {
            "providers": [
                "NeuronicLab\\Installer\\Providers\\InstallerServiceProvider"
            ]
        }
    },
    "config": {
        "sort-packages": true
    },
    "minimum-stability": "stable",
    "prefer-stable": true
}
```

### 2. Create .gitignore

```gitignore
/vendor/
/node_modules/
.env
.env.backup
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
.DS_Store
Thumbs.db
```

### 3. Add .gitattributes

```gitattributes
* text=auto
*.php text eol=lf
*.js text eol=lf
*.css text eol=lf
*.md text eol=lf
*.json text eol=lf
*.yml text eol=lf
*.yaml text eol=lf

# Exclude development files from distribution
/.github export-ignore
/tests export-ignore
/.gitignore export-ignore
/.gitattributes export-ignore
/phpunit.xml export-ignore
/verify-package.php export-ignore
```

## Version Management

### Semantic Versioning

Follow [Semantic Versioning](https://semver.org/) (SemVer):

- **MAJOR** (1.0.0): Breaking changes
- **MINOR** (1.1.0): New features, backward compatible
- **PATCH** (1.1.1): Bug fixes, backward compatible

### Version Tagging

```bash
# Create and push version tags
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# List all tags
git tag -l
```

### Release Branches

```bash
# Create release branch
git checkout -b release/1.0.0
git push -u origin release/1.0.0

# Merge to main after testing
git checkout main
git merge release/1.0.0
git push origin main
```

## Publishing to Packagist

### 1. Create Packagist Account

1. Visit [packagist.org](https://packagist.org)
2. Sign up or log in with GitHub
3. Verify your email address

### 2. Prepare GitHub Repository

```bash
# Initialize git repository
git init
git add .
git commit -m "Initial commit: NeuronicLab Installer v1.0.0"

# Add remote repository
git remote add origin https://github.com/neuroniclab/installer.git
git branch -M main
git push -u origin main
```

### 3. Submit to Packagist

1. Go to [packagist.org/packages/submit](https://packagist.org/packages/submit)
2. Enter your repository URL: `https://github.com/neuroniclab/installer`
3. Click "Check" to validate
4. Submit the package

### 4. Set Up Auto-Updates

Configure GitHub webhook for automatic updates:

1. Go to your GitHub repository settings
2. Navigate to "Webhooks"
3. Add webhook URL from Packagist
4. Set content type to `application/json`
5. Select "Just the push event"

### 5. Package Verification

After submission, verify:
- Package appears on Packagist
- Installation works: `composer require neuroniclab/installer`
- Documentation displays correctly
- Download statistics are tracking

## Private Repository Setup

### Option 1: Private Packagist

1. Sign up for [Private Packagist](https://packagist.com)
2. Create organization
3. Add private repository
4. Configure team access

### Option 2: Satis (Self-Hosted)

```bash
# Install Satis
composer create-project composer/satis

# Create satis.json
{
    "name": "NeuronicLab Private Repository",
    "homepage": "https://packages.neuroniclab.com",
    "repositories": [
        {
            "type": "vcs",
            "url": "https://github.com/neuroniclab/installer"
        }
    ],
    "require-all": true
}

# Build repository
php bin/satis build satis.json public/
```

### Option 3: GitLab Package Registry

```yaml
# .gitlab-ci.yml
stages:
  - publish

publish:
  stage: publish
  script:
    - composer config repositories.gitlab composer https://gitlab.com/api/v4/group/GROUP_ID/-/packages/composer
    - composer config http-basic.gitlab.com gitlab-ci-token $CI_JOB_TOKEN
  only:
    - tags
```

## Documentation and Marketing

### 1. README Optimization

Ensure your README includes:
- Clear description and features
- Installation instructions
- Usage examples
- Configuration options
- Contributing guidelines
- License information

### 2. Package Keywords

Optimize for discoverability:
```json
"keywords": [
    "laravel",
    "installer",
    "package",
    "setup",
    "deployment",
    "web-installer",
    "database-setup",
    "license-verification"
]
```

### 3. Social Media and Promotion

- Announce on Laravel communities
- Share on Twitter with #Laravel hashtag
- Post in relevant Discord/Slack channels
- Write blog posts about features
- Create video tutorials

### 4. Package Statistics

Monitor package adoption:
- Packagist download statistics
- GitHub stars and forks
- Issue reports and feedback
- Community engagement

## Maintenance and Updates

### 1. Regular Updates

```bash
# Check for dependency updates
composer outdated

# Update dependencies
composer update

# Test after updates
php verify-package.php
```

### 2. Security Updates

- Monitor security advisories
- Update dependencies promptly
- Test security patches
- Communicate security fixes

### 3. Laravel Compatibility

Maintain compatibility with Laravel versions:
- Test with new Laravel releases
- Update version constraints
- Provide migration guides
- Support LTS versions

### 4. Community Engagement

- Respond to issues promptly
- Review and merge pull requests
- Maintain documentation
- Provide support in forums

## Quality Assurance

### 1. Automated Testing

Set up CI/CD pipeline:

```yaml
# .github/workflows/tests.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php: [8.1, 8.2, 8.3]
        laravel: [10.0, 11.0]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php }}
    
    - name: Install dependencies
      run: composer install
    
    - name: Run verification
      run: php verify-package.php
```

### 2. Code Quality Tools

```bash
# Install quality tools
composer require --dev phpstan/phpstan
composer require --dev squizlabs/php_codesniffer

# Run analysis
vendor/bin/phpstan analyse src
vendor/bin/phpcs src --standard=PSR12
```

### 3. Documentation Testing

- Test all code examples
- Verify installation instructions
- Check link validity
- Ensure screenshots are current

## Distribution Strategies

### 1. Multi-Channel Distribution

- **Packagist**: Primary public distribution
- **Private repositories**: For premium versions
- **Direct download**: ZIP packages for non-Composer users
- **Docker images**: Containerized installations

### 2. Licensing Models

- **Open Source**: MIT license on Packagist
- **Commercial**: Paid licenses for extended features
- **Freemium**: Basic free, premium paid features
- **Enterprise**: Custom licensing for large organizations

### 3. Support Tiers

- **Community**: GitHub issues and discussions
- **Professional**: Email support and priority fixes
- **Enterprise**: Dedicated support and custom development

### 4. Release Channels

- **Stable**: Production-ready releases
- **Beta**: Feature previews and testing
- **Alpha**: Early development versions
- **LTS**: Long-term support versions

## Post-Publishing Checklist

### ✅ Immediate Actions
- [ ] Verify package installation works
- [ ] Test with fresh Laravel project
- [ ] Check Packagist page displays correctly
- [ ] Confirm auto-update webhook works

### ✅ First Week
- [ ] Monitor download statistics
- [ ] Respond to initial feedback
- [ ] Fix any reported issues
- [ ] Update documentation based on user questions

### ✅ Ongoing
- [ ] Regular dependency updates
- [ ] Security monitoring
- [ ] Community engagement
- [ ] Feature development based on feedback

---

**Remember**: Publishing is just the beginning. Successful packages require ongoing maintenance, community engagement, and continuous improvement based on user feedback.

**Package**: neuroniclab/installer  
**Version**: 1.0.0  
**Last Updated**: January 2024
