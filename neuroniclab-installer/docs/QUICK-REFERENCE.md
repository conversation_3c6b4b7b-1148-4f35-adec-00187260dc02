# NeuronicLab Installer - Quick Reference Guide

## Installation Commands

```bash
# Install package
composer require neuroniclab/installer

# Install all assets
php artisan neuroniclab:install

# Publish specific components
php artisan neuroniclab:publish --tag=config
php artisan neuroniclab:publish --tag=assets
php artisan neuroniclab:publish --tag=views
php artisan neuroniclab:publish --tag=database
php artisan neuroniclab:publish --tag=lang
```

## Configuration Quick Setup

### Basic Configuration
```php
// config/neuroniclab-installer.php
'ui' => [
    'app_name' => 'Your App Name',
    'logo' => 'path/to/logo.png',
    'primary_color' => '#007bff',
],

'license' => [
    'enabled' => true,
    'verification_url' => 'https://your-domain.com/api/verify',
    'item_name' => 'Your Product',
],

'database' => [
    'schema_file' => 'database/neuroniclab-installer/schema.sql',
    'mysql_path' => '/usr/bin/mysql',
],
```

### AppServiceProvider Integration
```php
// app/Providers/AppServiceProvider.php
public function boot()
{
    if (!app()->runningInConsole() && 
        !file_exists(storage_path('installed')) && 
        !request()->is('install') && 
        !request()->is('install/*')) {
        header("Location: install/");
        exit;
    }
}
```

## Route Names

- `NeuronicLabInstaller::welcome`
- `NeuronicLabInstaller::requirements`
- `NeuronicLabInstaller::permissions`
- `NeuronicLabInstaller::license`
- `NeuronicLabInstaller::environment`
- `NeuronicLabInstaller::environmentWizard`
- `NeuronicLabInstaller::database`
- `NeuronicLabInstaller::final`

## File Locations After Installation

```
config/neuroniclab-installer.php          # Main configuration
public/neuroniclab-installer/             # Assets (CSS, fonts, images)
resources/views/vendor/neuroniclab-installer/  # Blade templates
database/neuroniclab-installer/schema.sql # Database schema
resources/lang/vendor/neuroniclab-installer/   # Language files
storage/installed                         # Installation marker file
```

## Customization Examples

### Custom Controller
```php
namespace App\Http\Controllers\Installer;

use NeuronicLab\Installer\Controllers\EnvironmentController as BaseController;

class EnvironmentController extends BaseController
{
    public function environmentWizard()
    {
        // Custom logic here
        return parent::environmentWizard();
    }
}
```

### Custom Validation
```php
'environment' => [
    'form' => [
        'rules' => [
            'app_name' => 'required|string|max:50',
            'custom_field' => 'required|string',
        ],
    ],
],
```

### Custom CSS
```php
'ui' => [
    'custom_css' => '
        .header { background: #custom-color; }
        .button { background: #button-color; }
    ',
],
```

## Troubleshooting Commands

```bash
# Verify package structure
php verify-package.php

# Check routes
php artisan route:list | grep NeuronicLabInstaller

# Clear caches
php artisan route:clear
php artisan config:clear
php artisan view:clear

# Re-publish assets
php artisan neuroniclab:publish --tag=assets --force

# Check configuration
php artisan tinker
>>> config('neuroniclab-installer')
```

## Common Issues & Solutions

### Permission Errors
```bash
chmod -R 775 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

### Database Import Issues
- Check MySQL path in config
- Verify database credentials
- Ensure SQL file is valid

### Asset Loading Problems
```bash
php artisan neuroniclab:publish --tag=assets --force
```

### License Verification Fails
- Check internet connectivity
- Verify license server URL
- Validate credentials format

## Environment Variables

```env
# .env file options
INSTALLER_LICENSE_ENABLED=true
INSTALLER_LICENSE_URL=https://your-domain.com/api/verify
INSTALLER_MYSQL_PATH=/usr/bin/mysql
APP_DEBUG=true  # For troubleshooting
```

## Testing Checklist

- [ ] Package verification script passes
- [ ] Clean Laravel installation works
- [ ] All installation steps complete
- [ ] Browser testing successful
- [ ] Commands work correctly
- [ ] Configuration loads properly
- [ ] Assets display correctly
- [ ] Database import succeeds

## Security Notes

- Remove installer access in production
- Use HTTPS for license verification
- Secure database credentials
- Set proper file permissions
- Validate all user inputs

## Support Resources

- **Documentation**: USER-GUIDE.md
- **Integration**: INTEGRATION.md
- **Publishing**: PUBLISHING-GUIDE.md
- **Package Verification**: `php verify-package.php`
- **Laravel Logs**: `storage/logs/laravel.log`

---

**Package**: neuroniclab/installer  
**Version**: 1.0.0  
**Access**: http://your-domain.com/install
