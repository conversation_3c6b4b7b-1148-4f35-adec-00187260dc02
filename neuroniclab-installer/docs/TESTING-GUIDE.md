# NeuronicLab Installer - Testing Guide

## Overview

This guide provides comprehensive testing procedures to ensure the NeuronicLab Installer package works correctly across different environments and scenarios.

## Pre-Testing Setup

### Environment Requirements
- PHP 8.1+
- Laravel 10.0+
- MySQL/MariaDB
- Web server (Apache/Nginx)
- Composer

### Test Environments
1. **Local Development** - XAMPP/MAMP/Laravel Valet
2. **Docker Container** - Isolated testing environment
3. **Staging Server** - Production-like environment
4. **Fresh Laravel Installation** - Clean testing ground

## Testing Levels

### 1. Package Structure Testing

#### Verification Script
```bash
cd neuroniclab-installer
php verify-package.php
```

**Expected Output:**
```
✅ ALL CHECKS PASSED! Package is ready for use.
```

#### Manual Structure Check
```bash
# Verify directory structure
ls -la
# Should show: src/, resources/, assets/, config/, database/, docs/

# Check composer.json
cat composer.json | jq '.name'
# Should output: "neuroniclab/installer"

# Verify namespace
grep -r "NeuronicLab\\Installer" src/
# Should show all PHP files with correct namespace
```

### 2. Integration Testing

#### Test with Fresh Laravel Project

```bash
# Create new Laravel project
composer create-project laravel/laravel test-installer
cd test-installer

# Method 1: Local Package Testing
# Add to composer.json
{
    "repositories": [
        {
            "type": "path",
            "url": "../neuroniclab-installer"
        }
    ],
    "require": {
        "neuroniclab/installer": "*"
    }
}

# Install
composer install
php artisan neuroniclab:install

# Method 2: Packagist Testing (if published)
composer require neuroniclab/installer
php artisan neuroniclab:install
```

#### Verify Installation
```bash
# Check published files
ls -la config/neuroniclab-installer.php
ls -la public/neuroniclab-installer/
ls -la resources/views/vendor/neuroniclab-installer/
ls -la database/neuroniclab-installer/

# Check routes
php artisan route:list | grep NeuronicLabInstaller

# Test configuration loading
php artisan tinker
>>> config('neuroniclab-installer.ui.app_name')
>>> exit
```

### 3. Functional Testing

#### Browser Testing Checklist

**Setup Test Environment:**
```bash
# Start development server
php artisan serve

# Or configure virtual host
# http://test-installer.local
```

**Test Each Installation Step:**

1. **Welcome Page** (`/install`)
   - [ ] Page loads without errors
   - [ ] UI displays correctly
   - [ ] "Next" button works
   - [ ] Responsive design on mobile

2. **Requirements Check** (`/install/requirements`)
   - [ ] PHP version check passes
   - [ ] All required extensions detected
   - [ ] Missing extensions highlighted
   - [ ] Navigation works

3. **Permissions Check** (`/install/permissions`)
   - [ ] Directory permissions verified
   - [ ] Write permissions detected
   - [ ] Permission issues highlighted
   - [ ] Fixes suggested for issues

4. **License Verification** (`/install/license`)
   - [ ] Form displays correctly
   - [ ] Validation works for required fields
   - [ ] License verification process
   - [ ] Error handling for invalid licenses
   - [ ] Success message on valid license

5. **Environment Setup** (`/install/environment/wizard`)
   - [ ] Database connection form
   - [ ] Form validation works
   - [ ] Database connection testing
   - [ ] Environment file generation
   - [ ] Error handling for connection failures

6. **Database Installation** (`/install/database`)
   - [ ] Schema import process
   - [ ] Progress indication
   - [ ] Error handling for import failures
   - [ ] Success confirmation

7. **Final Step** (`/install/final`)
   - [ ] Installation completion message
   - [ ] Summary of installed components
   - [ ] Link to application
   - [ ] Installation file creation

#### Command Testing

```bash
# Test all artisan commands
php artisan neuroniclab:install --help
php artisan neuroniclab:publish --help

# Test publishing individual components
php artisan neuroniclab:publish --tag=config
php artisan neuroniclab:publish --tag=assets
php artisan neuroniclab:publish --tag=views
php artisan neuroniclab:publish --tag=database
php artisan neuroniclab:publish --tag=lang

# Test force publishing
php artisan neuroniclab:publish --tag=assets --force
```

### 4. Configuration Testing

#### Test Configuration Options

```php
// Test in tinker
php artisan tinker

// Test UI configuration
>>> config('neuroniclab-installer.ui')

// Test license configuration
>>> config('neuroniclab-installer.license')

// Test database configuration
>>> config('neuroniclab-installer.database')

// Test requirements
>>> config('neuroniclab-installer.requirements')

// Test permissions
>>> config('neuroniclab-installer.permissions')
```

#### Test Environment Variables

```bash
# Add to .env
INSTALLER_LICENSE_ENABLED=false
INSTALLER_MYSQL_PATH=/custom/path/mysql

# Test configuration override
php artisan tinker
>>> config('neuroniclab-installer.license.enabled')
>>> config('neuroniclab-installer.database.mysql_path')
```

### 5. Error Handling Testing

#### Test Error Scenarios

1. **Database Connection Errors**
   - Invalid hostname
   - Wrong credentials
   - Non-existent database
   - Network connectivity issues

2. **File Permission Errors**
   - Read-only directories
   - Missing write permissions
   - Insufficient disk space

3. **License Verification Errors**
   - Invalid license codes
   - Network connectivity issues
   - Server timeout
   - Invalid response format

4. **Asset Loading Errors**
   - Missing CSS files
   - Broken image links
   - Font loading issues

#### Test Error Recovery

```bash
# Simulate permission errors
chmod 444 storage/
# Try installation, then fix
chmod 755 storage/

# Test with invalid database credentials
# Modify .env with wrong credentials
# Test installation process

# Test license verification with invalid data
# Use fake license credentials
# Verify error handling
```

### 6. Security Testing

#### Test Security Features

1. **Installation State Protection**
   ```bash
   # Create installed file
   touch storage/installed
   
   # Try to access installer
   curl http://localhost:8000/install
   # Should redirect or show 404
   
   # Remove installed file
   rm storage/installed
   ```

2. **Input Validation**
   - Test SQL injection attempts
   - Test XSS attempts
   - Test file upload vulnerabilities
   - Test CSRF protection

3. **License Verification Security**
   - Test with malformed requests
   - Test rate limiting
   - Test SSL/TLS verification

### 7. Performance Testing

#### Load Testing

```bash
# Test with large database schema
# Create large SQL file (>10MB)
cp large-schema.sql database/neuroniclab-installer/schema.sql

# Test import performance
time php artisan migrate

# Test with multiple concurrent requests
# Use Apache Bench or similar tool
ab -n 100 -c 10 http://localhost:8000/install
```

#### Memory Testing

```bash
# Monitor memory usage during installation
php -d memory_limit=128M artisan neuroniclab:install

# Test with limited memory
php -d memory_limit=64M artisan neuroniclab:install
```

### 8. Compatibility Testing

#### Laravel Version Testing

```bash
# Test with different Laravel versions
composer create-project laravel/laravel:^10.0 test-laravel-10
composer create-project laravel/laravel:^11.0 test-laravel-11

# Test package installation on each version
```

#### PHP Version Testing

```bash
# Test with different PHP versions
php8.1 -v && composer install
php8.2 -v && composer install
php8.3 -v && composer install
```

#### Database Testing

```bash
# Test with different databases
# MySQL 5.7, 8.0
# MariaDB 10.3, 10.4, 10.5
# PostgreSQL (if supported)
```

### 9. Regression Testing

#### Test After Changes

```bash
# Run full test suite after any code changes
php verify-package.php
php artisan test  # If unit tests exist

# Test critical paths
# 1. Fresh installation
# 2. Asset publishing
# 3. Configuration loading
# 4. Database import
```

### 10. User Acceptance Testing

#### Test User Scenarios

1. **First-time User**
   - Follow installation without prior knowledge
   - Document any confusion points
   - Test with minimal technical knowledge

2. **Experienced Developer**
   - Test customization options
   - Test integration with existing projects
   - Test advanced configuration

3. **System Administrator**
   - Test deployment scenarios
   - Test security configurations
   - Test maintenance procedures

## Automated Testing Setup

### PHPUnit Tests

```php
// tests/Feature/InstallerTest.php
<?php

namespace Tests\Feature;

use Tests\TestCase;

class InstallerTest extends TestCase
{
    public function test_installer_welcome_page()
    {
        $response = $this->get('/install');
        $response->assertStatus(200);
        $response->assertSee('NeuronicLab');
    }
    
    public function test_requirements_check()
    {
        $response = $this->get('/install/requirements');
        $response->assertStatus(200);
    }
    
    public function test_configuration_loading()
    {
        $config = config('neuroniclab-installer');
        $this->assertIsArray($config);
        $this->assertArrayHasKey('ui', $config);
    }
}
```

### CI/CD Pipeline

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php: [8.1, 8.2, 8.3]
        laravel: [10.0, 11.0]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php }}
    
    - name: Install dependencies
      run: composer install
    
    - name: Run verification
      run: php verify-package.php
    
    - name: Run tests
      run: vendor/bin/phpunit
```

## Test Documentation

### Test Results Template

```markdown
## Test Results - NeuronicLab Installer

**Date**: [Date]
**Version**: [Version]
**Tester**: [Name]
**Environment**: [Description]

### Test Summary
- [ ] Package Structure: PASS/FAIL
- [ ] Integration: PASS/FAIL
- [ ] Functional: PASS/FAIL
- [ ] Configuration: PASS/FAIL
- [ ] Error Handling: PASS/FAIL
- [ ] Security: PASS/FAIL
- [ ] Performance: PASS/FAIL
- [ ] Compatibility: PASS/FAIL

### Issues Found
1. [Issue description]
2. [Issue description]

### Recommendations
1. [Recommendation]
2. [Recommendation]
```

## Continuous Testing

### Regular Testing Schedule

- **Daily**: Automated tests via CI/CD
- **Weekly**: Manual functional testing
- **Monthly**: Full compatibility testing
- **Before Release**: Complete test suite

### Monitoring

- Set up monitoring for package downloads
- Track installation success rates
- Monitor error reports
- Collect user feedback

---

**Remember**: Testing is an ongoing process. Regular testing ensures package quality and user satisfaction.

**Package**: neuroniclab/installer  
**Version**: 1.0.0  
**Last Updated**: January 2024
