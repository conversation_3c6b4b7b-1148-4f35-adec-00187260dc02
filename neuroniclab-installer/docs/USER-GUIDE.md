# NeuronicLab Installer - Comprehensive User Guide

## Table of Contents

1. [Overview](#overview)
2. [System Requirements](#system-requirements)
3. [Installation](#installation)
4. [Configuration](#configuration)
5. [Usage](#usage)
6. [Customization](#customization)
7. [Troubleshooting](#troubleshooting)
8. [Advanced Features](#advanced-features)
9. [Security Considerations](#security-considerations)
10. [FAQ](#faq)

## Overview

NeuronicLab Installer is a comprehensive Laravel package that provides a complete web-based installation experience for Laravel applications. It features:

- **Beautiful UI**: Custom-branded installer interface with responsive design
- **License Verification**: Built-in license validation system with remote verification
- **Database Setup**: Automated database schema import and configuration
- **Requirements Checking**: PHP extensions and server requirements validation
- **Permission Validation**: File and directory permissions checking
- **Environment Configuration**: Guided environment setup wizard
- **Security Integration**: Built-in security middleware and installation state checking
- **Multi-language Support**: Internationalization ready

## System Requirements

### Server Requirements
- **PHP**: 8.1 or higher
- **<PERSON>vel**: 10.0 or higher
- **Database**: MySQL 5.7+ or MariaDB 10.3+
- **Web Server**: Apache 2.4+ or Nginx 1.18+

### PHP Extensions
- OpenSSL
- PDO
- Mbstring
- Tokenizer
- JSON
- cURL
- Fileinfo
- GD
- ZIP
- BCMath
- EXIF
- IMAP

### Server Permissions
The following directories must be writable:
- `storage/framework/`
- `storage/logs/`
- `storage/app/`
- `bootstrap/cache/`
- `public/files/` (if applicable)
- `resources/lang/` (if applicable)

## Installation

### Step 1: Install via Composer

```bash
composer require neuroniclab/installer
```

### Step 2: Install Package Assets

```bash
php artisan neuroniclab:install
```

This command will:
- Publish configuration files
- Copy assets (CSS, fonts, images)
- Install views and templates
- Set up database schema files
- Install language files

### Step 3: Manual Publishing (Alternative)

If you prefer manual control:

```bash
# Publish all assets
php artisan vendor:publish --provider="NeuronicLab\Installer\Providers\InstallerServiceProvider"

# Or publish specific components
php artisan neuroniclab:publish --tag=config
php artisan neuroniclab:publish --tag=assets
php artisan neuroniclab:publish --tag=views
php artisan neuroniclab:publish --tag=database
php artisan neuroniclab:publish --tag=lang
```

### Step 4: Configure Installation Check

Add installation checking to your `app/Providers/AppServiceProvider.php`:

```php
public function boot()
{
    // Only check installation for HTTP requests, not console commands
    if (!app()->runningInConsole() && 
        !file_exists(storage_path('installed')) && 
        !request()->is('install') && 
        !request()->is('install/*')) {
        header("Location: install/");
        exit;
    }
}
```

## Configuration

### Main Configuration File

Edit `config/neuroniclab-installer.php` to customize the installer:

#### Basic Settings
```php
'core' => [
    'minPhpVersion' => '8.1',
],

'ui' => [
    'app_name' => 'Your Application Name',
    'logo' => 'path/to/your/logo.png',
    'primary_color' => '#007bff',
    'custom_css' => null,
],
```

#### License Configuration
```php
'license' => [
    'enabled' => true,
    'verification_file' => 'vendor/mockery/mockery/verified',
    'verification_url' => 'https://your-domain.com/api/verify-license',
    'item_id' => 'your-item-id',
    'item_name' => 'Your Application',
    'required_fields' => [
        'email' => true,
        'username' => true,
        'purchase_code' => true,
    ],
],
```

#### Database Configuration
```php
'database' => [
    'schema_file' => 'database/neuroniclab-installer/schema.sql',
    'mysql_path' => '/usr/bin/mysql',
    'import_method' => 'auto', // 'mysql', 'php', or 'auto'
],
```

### Environment Variables

Add these to your `.env` file if needed:

```env
INSTALLER_LICENSE_ENABLED=true
INSTALLER_LICENSE_URL=https://your-domain.com/api/verify
INSTALLER_MYSQL_PATH=/usr/bin/mysql
```

## Usage

### Accessing the Installer

1. Navigate to `http://your-domain.com/install` in your browser
2. Follow the step-by-step installation process:

#### Step 1: Welcome
- Introduction and overview
- System compatibility check

#### Step 2: Requirements
- PHP version verification
- Extension availability check
- Server configuration validation

#### Step 3: Permissions
- Directory write permissions check
- File permission validation

#### Step 4: License Verification (if enabled)
- Enter license credentials
- Remote verification (if configured)
- License validation

#### Step 5: Environment Configuration
- Database connection setup
- Application configuration
- Environment file generation

#### Step 6: Database Installation
- Schema import
- Data seeding (if applicable)
- Database optimization

#### Step 7: Completion
- Installation summary
- Final configuration
- Access to application

### Post-Installation

After successful installation:
- The installer creates a `storage/installed` file
- Access to `/install` is automatically blocked
- Your application is ready to use

## Customization

### Custom Views

Publish and modify views:

```bash
php artisan neuroniclab:publish --tag=views
```

Views are located in `resources/views/vendor/neuroniclab-installer/`:
- `welcome.blade.php` - Welcome page
- `requirements.blade.php` - Requirements check
- `permissions.blade.php` - Permissions check
- `license.blade.php` - License verification
- `environment-wizard.blade.php` - Environment setup
- `finished.blade.php` - Completion page
- `layouts/master.blade.php` - Main layout

### Custom Styling

1. Publish assets:
```bash
php artisan neuroniclab:publish --tag=assets
```

2. Modify `public/neuroniclab-installer/css/style.min.css`

3. Or add custom CSS in configuration:
```php
'ui' => [
    'custom_css' => '
        .header { background: #your-color; }
        .button { background: #your-button-color; }
    ',
],
```

### Custom Controllers

Extend package controllers for custom functionality:

```php
<?php

namespace App\Http\Controllers\Installer;

use NeuronicLab\Installer\Controllers\EnvironmentController as BaseController;

class EnvironmentController extends BaseController
{
    public function environmentWizard()
    {
        // Add custom logic
        $customData = $this->getCustomData();
        
        return parent::environmentWizard()->with('customData', $customData);
    }
    
    private function getCustomData()
    {
        // Your custom logic here
        return [];
    }
}
```

### Custom Validation Rules

Add custom validation in configuration:

```php
'environment' => [
    'form' => [
        'rules' => [
            'app_name' => 'required|string|max:50',
            'custom_field' => 'required|string|min:3',
            'api_key' => 'required|string|size:32',
        ],
    ],
],
```

### Custom Database Schema

1. Replace the default schema file:
```bash
cp your-schema.sql database/neuroniclab-installer/schema.sql
```

2. Or update the configuration:
```php
'database' => [
    'schema_file' => 'path/to/your/schema.sql',
],
```

## Troubleshooting

### Common Issues

#### 1. Permission Errors
```bash
# Fix directory permissions
chmod -R 775 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

#### 2. Database Import Fails
- Check MySQL path configuration
- Verify database credentials
- Ensure database exists and is accessible
- Check SQL file syntax

#### 3. Assets Not Loading
```bash
# Re-publish assets
php artisan neuroniclab:publish --tag=assets --force
```

#### 4. License Verification Issues
- Check internet connectivity
- Verify license server URL
- Validate credentials format
- Check firewall settings

#### 5. Route Conflicts
- Clear route cache: `php artisan route:clear`
- Check for conflicting routes in your application

### Debug Mode

Enable debug mode for troubleshooting:

```env
APP_DEBUG=true
LOG_LEVEL=debug
```

Check logs in `storage/logs/laravel.log`

### Verification Commands

```bash
# Verify package structure
php verify-package.php

# Check configuration
php artisan tinker
>>> config('neuroniclab-installer')

# Test routes
php artisan route:list | grep NeuronicLabInstaller
```

## Advanced Features

### Custom License Verification

Implement your own license verification endpoint:

```php
// routes/api.php
Route::post('/verify-license', function (Request $request) {
    $email = $request->input('email');
    $username = $request->input('username');
    $purchaseCode = $request->input('purchase_code');
    $domain = $request->input('domain');
    
    // Your verification logic here
    $isValid = YourLicenseService::verify($email, $username, $purchaseCode, $domain);
    
    return response()->json([
        'status' => $isValid ? 'success' : 'error',
        'message' => $isValid ? 'License verified' : 'Invalid license'
    ]);
});
```

### Custom Middleware Integration

Register custom middleware:

```php
// app/Http/Kernel.php
protected $middlewareAliases = [
    'neuroniclab-install' => \NeuronicLab\Installer\Middleware\CanInstall::class,
    'neuroniclab-security' => \NeuronicLab\Installer\Middleware\Security::class,
    'custom-installer' => \App\Http\Middleware\CustomInstaller::class,
];
```

### Multi-Environment Support

Configure different settings per environment:

```php
// config/neuroniclab-installer.php
'license' => [
    'enabled' => env('INSTALLER_LICENSE_ENABLED', true),
    'verification_url' => env('INSTALLER_LICENSE_URL'),
],

'database' => [
    'mysql_path' => env('INSTALLER_MYSQL_PATH', 'mysql'),
],
```

## Security Considerations

### 1. Remove Installer After Deployment

For production environments:

```php
// In production, disable installer access
if (app()->environment('production') && file_exists(storage_path('installed'))) {
    // Remove installer routes or add additional security
}
```

### 2. Secure License Verification

- Use HTTPS for license verification
- Implement rate limiting
- Validate all inputs
- Use secure token-based authentication

### 3. Database Security

- Use strong database credentials
- Limit database user permissions
- Validate SQL file contents
- Use prepared statements

### 4. File Permissions

- Set restrictive permissions after installation
- Remove write access where not needed
- Secure configuration files

## FAQ

### Q: Can I use this installer with existing Laravel applications?
A: Yes, but ensure you backup your application first and test thoroughly in a staging environment.

### Q: How do I disable license verification?
A: Set `'enabled' => false` in the license configuration section.

### Q: Can I customize the installation steps?
A: Yes, you can enable/disable steps in the configuration and create custom controllers.

### Q: How do I handle large database schemas?
A: Use the MySQL command-line import method and increase PHP memory/execution limits.

### Q: Is the installer mobile-friendly?
A: Yes, the installer uses responsive design and works on mobile devices.

### Q: Can I use custom database drivers?
A: The installer supports standard Laravel database drivers (MySQL, PostgreSQL, SQLite, SQL Server).

### Q: How do I update the installer package?
A: Run `composer update neuroniclab/installer` and re-publish assets if needed.

### Q: Can I run the installer multiple times?
A: No, once installed, access is blocked. Delete `storage/installed` to re-run if needed.

## Support

For additional support:
- Check the package documentation
- Review configuration options
- Test with the verification script
- Check Laravel logs for errors
- Ensure all requirements are met

---

**Version**: 1.0.0  
**Last Updated**: January 2024  
**Package**: neuroniclab/installer
