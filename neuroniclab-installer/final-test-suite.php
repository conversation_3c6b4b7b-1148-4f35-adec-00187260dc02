<?php

/**
 * Final Test Suite for NeuronicLab Installer
 * 
 * This script runs all comprehensive tests to verify the package works correctly
 * after removing the rachidlaasri/laravel-installer dependency.
 */

echo "🧪 NeuronicLab Installer - Final Test Suite\n";
echo str_repeat("=", 60) . "\n\n";

$startTime = microtime(true);
$totalTests = 0;
$passedTests = 0;
$failedTests = 0;
$errors = [];

// Test 1: Independence Verification
echo "📋 Test 1: Independence Verification\n";
echo str_repeat("-", 40) . "\n";

$independenceTests = [
    'No rachidlaasri in composer.json' => function() {
        $composer = json_decode(file_get_contents(__DIR__ . '/composer.json'), true);
        return !isset($composer['require']['rachidlaasri/laravel-installer']);
    },
    
    'Required dependencies exist' => function() {
        $composer = json_decode(file_get_contents(__DIR__ . '/composer.json'), true);
        $required = ['php', 'laravel/framework', 'nesbot/carbon'];
        foreach ($required as $dep) {
            if (!isset($composer['require'][$dep])) return false;
        }
        return true;
    },
    
    'No rachidlaasri references in source' => function() {
        $files = glob(__DIR__ . '/src/**/*.php');
        foreach ($files as $file) {
            $content = file_get_contents($file);
            if (strpos($content, 'RachidLaasri\\LaravelInstaller') !== false) {
                return false;
            }
        }
        return true;
    }
];

foreach ($independenceTests as $testName => $testFunction) {
    $totalTests++;
    if ($testFunction()) {
        echo "✅ {$testName}: PASSED\n";
        $passedTests++;
    } else {
        echo "❌ {$testName}: FAILED\n";
        $failedTests++;
        $errors[] = "Independence Test: {$testName}";
    }
}

// Test 2: File Structure Verification
echo "\n📋 Test 2: File Structure Verification\n";
echo str_repeat("-", 40) . "\n";

$requiredFiles = [
    'composer.json',
    'README.md',
    'LICENSE',
    'CHANGELOG.md',
    'config/neuroniclab-installer.php',
    'src/Helpers/DatabaseManager.php',
    'src/Helpers/EnvironmentManager.php',
    'src/Helpers/FinalInstallManager.php',
    'src/Helpers/InstalledFileManager.php',
    'src/Helpers/PermissionsChecker.php',
    'src/Helpers/RequirementsChecker.php',
    'src/Events/EnvironmentSaved.php',
    'src/Events/LaravelInstallerFinished.php',
    'src/Controllers/DatabaseController.php',
    'src/Controllers/EnvironmentController.php',
    'src/Controllers/FinalController.php',
    'src/Controllers/LicenseController.php',
    'src/Controllers/PermissionsController.php',
    'src/Controllers/RequirementsController.php',
    'src/Controllers/WelcomeController.php',
];

foreach ($requiredFiles as $file) {
    $totalTests++;
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "✅ {$file}: EXISTS\n";
        $passedTests++;
    } else {
        echo "❌ {$file}: MISSING\n";
        $failedTests++;
        $errors[] = "File Structure: Missing {$file}";
    }
}

// Test 3: PHP Syntax Validation
echo "\n📋 Test 3: PHP Syntax Validation\n";
echo str_repeat("-", 40) . "\n";

$phpFiles = array_merge(
    glob(__DIR__ . '/src/Helpers/*.php'),
    glob(__DIR__ . '/src/Events/*.php'),
    glob(__DIR__ . '/src/Controllers/*.php'),
    glob(__DIR__ . '/src/Commands/*.php'),
    glob(__DIR__ . '/src/Middleware/*.php'),
    glob(__DIR__ . '/src/Providers/*.php')
);

foreach ($phpFiles as $file) {
    $totalTests++;
    $output = shell_exec("php -l {$file} 2>&1");
    if (strpos($output, 'No syntax errors') !== false) {
        echo "✅ " . basename($file) . ": SYNTAX OK\n";
        $passedTests++;
    } else {
        echo "❌ " . basename($file) . ": SYNTAX ERROR\n";
        $failedTests++;
        $errors[] = "Syntax Error: " . basename($file);
    }
}

// Test 4: Class Method Verification
echo "\n📋 Test 4: Class Method Verification\n";
echo str_repeat("-", 40) . "\n";

$requiredMethods = [
    'DatabaseManager' => ['migrateAndSeed'],
    'EnvironmentManager' => ['getEnvContent', 'saveFileWizard', 'saveFileClassic'],
    'FinalInstallManager' => ['runFinal'],
    'InstalledFileManager' => ['update'],
    'PermissionsChecker' => ['check'],
    'RequirementsChecker' => ['check', 'checkPHPversion'],
];

foreach ($requiredMethods as $className => $methods) {
    $filePath = __DIR__ . "/src/Helpers/{$className}.php";
    $content = file_get_contents($filePath);
    
    foreach ($methods as $method) {
        $totalTests++;
        if (strpos($content, "function {$method}(") !== false) {
            echo "✅ {$className}::{$method}(): EXISTS\n";
            $passedTests++;
        } else {
            echo "❌ {$className}::{$method}(): MISSING\n";
            $failedTests++;
            $errors[] = "Method Missing: {$className}::{$method}()";
        }
    }
}

// Test 5: Controller Import Verification
echo "\n📋 Test 5: Controller Import Verification\n";
echo str_repeat("-", 40) . "\n";

$controllerImports = [
    'DatabaseController.php' => ['NeuronicLab\Installer\Helpers\DatabaseManager'],
    'EnvironmentController.php' => [
        'NeuronicLab\Installer\Events\EnvironmentSaved',
        'NeuronicLab\Installer\Helpers\EnvironmentManager'
    ],
    'FinalController.php' => [
        'NeuronicLab\Installer\Events\LaravelInstallerFinished',
        'NeuronicLab\Installer\Helpers\EnvironmentManager',
        'NeuronicLab\Installer\Helpers\FinalInstallManager',
        'NeuronicLab\Installer\Helpers\InstalledFileManager'
    ],
    'PermissionsController.php' => ['NeuronicLab\Installer\Helpers\PermissionsChecker'],
    'RequirementsController.php' => ['NeuronicLab\Installer\Helpers\RequirementsChecker'],
];

foreach ($controllerImports as $controller => $imports) {
    $filePath = __DIR__ . "/src/Controllers/{$controller}";
    $content = file_get_contents($filePath);
    
    foreach ($imports as $import) {
        $totalTests++;
        if (strpos($content, $import) !== false) {
            echo "✅ {$controller}: {$import}\n";
            $passedTests++;
        } else {
            echo "❌ {$controller}: MISSING {$import}\n";
            $failedTests++;
            $errors[] = "Import Missing: {$controller} -> {$import}";
        }
    }
}

// Test 6: Configuration Verification
echo "\n📋 Test 6: Configuration Verification\n";
echo str_repeat("-", 40) . "\n";

$config = include __DIR__ . '/config/neuroniclab-installer.php';
$requiredConfigSections = ['core', 'requirements', 'permissions', 'license', 'database', 'ui'];

foreach ($requiredConfigSections as $section) {
    $totalTests++;
    if (isset($config[$section])) {
        echo "✅ Config section '{$section}': EXISTS\n";
        $passedTests++;
    } else {
        echo "❌ Config section '{$section}': MISSING\n";
        $failedTests++;
        $errors[] = "Config Missing: {$section} section";
    }
}

// Test 7: Translation Keys
echo "\n📋 Test 7: Translation Keys Verification\n";
echo str_repeat("-", 40) . "\n";

$translations = include __DIR__ . '/resources/lang/en/installer_messages.php';
$requiredTranslations = [
    'final.log_file_created',
    'final.log_file_updated',
    'environment.success',
    'environment.errors'
];

foreach ($requiredTranslations as $key) {
    $totalTests++;
    $keys = explode('.', $key);
    $value = $translations;
    $exists = true;
    
    foreach ($keys as $k) {
        if (!isset($value[$k])) {
            $exists = false;
            break;
        }
        $value = $value[$k];
    }
    
    if ($exists) {
        echo "✅ Translation '{$key}': EXISTS\n";
        $passedTests++;
    } else {
        echo "❌ Translation '{$key}': MISSING\n";
        $failedTests++;
        $errors[] = "Translation Missing: {$key}";
    }
}

// Calculate execution time
$endTime = microtime(true);
$executionTime = round($endTime - $startTime, 2);

// Display final results
echo "\n" . str_repeat("=", 60) . "\n";
echo "🏁 FINAL TEST RESULTS\n";
echo str_repeat("=", 60) . "\n";
echo "Total Tests: {$totalTests}\n";
echo "✅ Passed: {$passedTests}\n";
echo "❌ Failed: {$failedTests}\n";
echo "⏱️  Execution Time: {$executionTime} seconds\n";
echo "📊 Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

if ($failedTests > 0) {
    echo "❌ FAILED TESTS:\n";
    echo str_repeat("-", 40) . "\n";
    foreach ($errors as $error) {
        echo "  • {$error}\n";
    }
    echo "\n❌ Some tests failed. Please review and fix the issues above.\n";
    exit(1);
} else {
    echo "🎉 ALL TESTS PASSED!\n";
    echo "✨ NeuronicLab Installer is working correctly and is completely independent!\n\n";
    echo "📋 Summary of Achievements:\n";
    echo "  ✅ Removed rachidlaasri/laravel-installer dependency\n";
    echo "  ✅ Created 6 custom helper classes\n";
    echo "  ✅ Created 2 custom event classes\n";
    echo "  ✅ Updated all controllers to use new classes\n";
    echo "  ✅ All PHP syntax is valid\n";
    echo "  ✅ All required methods exist\n";
    echo "  ✅ All imports are correct\n";
    echo "  ✅ Configuration is complete\n";
    echo "  ✅ Translation keys are available\n\n";
    echo "🚀 Ready for production use!\n";
    exit(0);
}
