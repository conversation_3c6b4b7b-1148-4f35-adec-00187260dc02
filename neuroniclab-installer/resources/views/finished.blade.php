@extends('neuroniclab-installer::layouts.master')

@section('template_title')
    {{ trans('neuroniclab-installer::installer_messages.final.templateTitle') }}
@endsection

@section('title')
    <i class="fa fa-flag-checkered fa-fw" aria-hidden="true"></i>
    {{ trans('neuroniclab-installer::installer_messages.final.title') }}
@endsection

@section('container')
    <div class="buttons">
        <a href="{{ url('/') }}" class="button">
            {{ trans('neuroniclab-installer::installer_messages.final.exit') }}
        </a>
    </div>

    <p class="text-center">
        {{ trans('neuroniclab-installer::installer_messages.final.finished') }}
    </p>

    @if(isset($finalMessages) && is_array($finalMessages))
        <ul>
            @foreach($finalMessages as $message)
                <li>{{ $message['message'] }}</li>
            @endforeach
        </ul>
    @endif

    @if(isset($finalStatusMessage))
        <p class="text-center">
            <strong>{{ $finalStatusMessage }}</strong>
        </p>
    @endif
@endsection
