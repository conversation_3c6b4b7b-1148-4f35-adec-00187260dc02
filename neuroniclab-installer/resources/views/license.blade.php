@extends('neuroniclab-installer::layouts.master')

@section('template_title')
    {{ trans('neuroniclab-installer::installer_messages.license.templateTitle') }}
@endsection

@section('title')
    <i class="fa fa-key fa-fw" aria-hidden="true"></i>
    {{ trans('neuroniclab-installer::installer_messages.license.title') }}
@endsection

@section('container')
    <div class="tabs tabs-full">

        <form method="post" action="{{ route('NeuronicLabInstaller::licenseCheck') }}" class="tabs-wrap">
            @if(session()->has('license_error'))
                <div class="alert alert-danger" id="error_alert">
                    <button type="button" class="close" id="close_alert" data-dismiss="alert" aria-hidden="true">
                        <i class="fa fa-close" aria-hidden="true"></i>
                    </button>
                    <p style="margin-bottom: 0px;">{{session()->get('license_error')}}</p>
                </div>
            @endif

            @if(session()->has('license_success'))
                <div class="alert alert-success" id="success_alert">
                    <button type="button" class="close" id="close_success_alert" data-dismiss="alert" aria-hidden="true">
                        <i class="fa fa-close" aria-hidden="true"></i>
                    </button>
                    <p style="margin-bottom: 0px;">{{session()->get('license_success')}}</p>
                </div>
            @endif

            @if(config('neuroniclab-installer.license.enabled'))
                <div class="alert alert-warning" style="background-color: #fff3cd; color: #856404;">
                    <p style="margin-bottom: 0px;">{{ trans('neuroniclab-installer::installer_messages.license.connection_warning') }}</p>
                </div>
            @endif

            <input type="hidden" name="_token" value="{{ csrf_token() }}">

            <div>
                @if(config('neuroniclab-installer.license.required_fields.email'))
                <div class="form-group {{ $errors->has('email') ? ' has-error ' : '' }}">
                    <label for="email">
                        {{ trans('neuroniclab-installer::installer_messages.license.form.email') }}
                    </label>
                    <input type="email" name="email" id="email" value="{{ old('email') }}" placeholder="{{ trans('neuroniclab-installer::installer_messages.license.form.email_placeholder') }}" />
                    <p>{{ trans('neuroniclab-installer::installer_messages.license.form.email_description') }}</p>
                    @if ($errors->has('email'))
                        <span class="error-block">
                            <i class="fa fa-fw fa-exclamation-triangle" aria-hidden="true"></i>
                            {{ $errors->first('email') }}
                        </span>
                    @endif
                </div>
                @endif

                @if(config('neuroniclab-installer.license.required_fields.username'))
                <div class="form-group {{ $errors->has('username') ? ' has-error ' : '' }}">
                    <label for="username">
                        {{ trans('neuroniclab-installer::installer_messages.license.form.username') }}
                    </label>
                    <input type="text" name="username" id="username" value="{{ old('username') }}" placeholder="{{ trans('neuroniclab-installer::installer_messages.license.form.username_placeholder') }}" />
                    @if ($errors->has('username'))
                        <span class="error-block">
                            <i class="fa fa-fw fa-exclamation-triangle" aria-hidden="true"></i>
                            {{ $errors->first('username') }}
                        </span>
                    @endif
                </div>
                @endif

                @if(config('neuroniclab-installer.license.required_fields.purchase_code'))
                <div class="form-group {{ $errors->has('purchase_code') ? ' has-error ' : '' }}">
                    <label for="purchase_code">
                        {{ trans('neuroniclab-installer::installer_messages.license.form.purchase_code') }}
                    </label>
                    <input type="text" name="purchase_code" id="purchase_code" value="{{ old('purchase_code') }}" placeholder="{{ trans('neuroniclab-installer::installer_messages.license.form.purchase_code_placeholder') }}" />
                    @if ($errors->has('purchase_code'))
                        <span class="error-block">
                            <i class="fa fa-fw fa-exclamation-triangle" aria-hidden="true"></i>
                            {{ $errors->first('purchase_code') }}
                        </span>
                    @endif
                </div>
                @endif

                <div class="buttons">
                    <button class="button" type="submit" style="font-size: 14px;">
                        {{ trans('neuroniclab-installer::installer_messages.license.form.verify_button') }}
                        <i class="fa fa-angle-right fa-fw" aria-hidden="true"></i>
                    </button>
                </div>
            </div>

        </form>

    </div>
@endsection

@section('scripts')
<script type="text/javascript">
    var successAlert = document.getElementById('success_alert');
    var closeSuccessAlert = document.getElementById('close_success_alert');
    if(closeSuccessAlert){
        closeSuccessAlert.onclick = function() {
            successAlert.style.display = "none";
        };
    }
</script>
@endsection
