@extends('neuroniclab-installer::layouts.master')

@section('template_title')
    {{ trans('neuroniclab-installer::installer_messages.welcome.templateTitle') }}
@endsection

@section('title')
    {{ config('neuroniclab-installer.ui.app_name', 'NeuronicLab') }} {{ trans('neuroniclab-installer::installer_messages.welcome.title') }}
@endsection

@section('container')
    <p class="text-center">
        {{ trans('neuroniclab-installer::installer_messages.welcome.message') }}
    </p>
    
    @if(config('neuroniclab-installer.ui.logo'))
        <div class="text-center" style="margin: 20px 0;">
            <img src="{{ asset(config('neuroniclab-installer.ui.logo')) }}" alt="Logo" style="max-height: 100px;">
        </div>
    @endif
    
    <p class="text-center">
        <a href="{{ route('NeuronicLabInstaller::requirements') }}" class="button">
            {{ trans('neuroniclab-installer::installer_messages.welcome.next') }}
            <i class="fa fa-angle-right fa-fw" aria-hidden="true"></i>
        </a>
    </p>
@endsection
