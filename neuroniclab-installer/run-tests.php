<?php

/**
 * Comprehensive Test Runner for NeuronicLab Installer
 * 
 * This script runs all tests to verify the package works correctly
 * after removing the rachidlaasri/laravel-installer dependency.
 */

echo "🧪 NeuronicLab Installer - Comprehensive Test Suite\n";
echo str_repeat("=", 60) . "\n\n";

$startTime = microtime(true);
$totalTests = 0;
$passedTests = 0;
$failedTests = 0;
$errors = [];

// Test categories to run
$testCategories = [
    'Basic Independence Check' => 'test-independence.php',
    'Package Structure Verification' => 'verify-package.php',
    'PHPUnit Tests' => 'phpunit'
];

foreach ($testCategories as $categoryName => $testCommand) {
    echo "📋 Running: {$categoryName}\n";
    echo str_repeat("-", 40) . "\n";
    
    if ($testCommand === 'phpunit') {
        // Run PHPUnit tests if available
        if (file_exists(__DIR__ . '/vendor/bin/phpunit')) {
            $command = __DIR__ . '/vendor/bin/phpunit tests/';
        } else {
            // Fallback to manual test execution
            $testFiles = glob(__DIR__ . '/tests/*Test.php');
            $manualTestResults = runManualTests($testFiles);
            $totalTests += $manualTestResults['total'];
            $passedTests += $manualTestResults['passed'];
            $failedTests += $manualTestResults['failed'];
            $errors = array_merge($errors, $manualTestResults['errors']);
            continue;
        }
    } else {
        $command = "php {$testCommand}";
    }
    
    $output = shell_exec($command . ' 2>&1');
    $returnCode = 0;
    exec($command, $outputArray, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ PASSED\n";
        $passedTests++;
    } else {
        echo "❌ FAILED\n";
        $failedTests++;
        $errors[] = [
            'category' => $categoryName,
            'output' => $output
        ];
    }
    
    $totalTests++;
    echo "\n";
}

// Run additional manual verification tests
echo "📋 Running: Manual Verification Tests\n";
echo str_repeat("-", 40) . "\n";

$manualTests = [
    'File Structure' => function() {
        $requiredFiles = [
            'composer.json',
            'src/Helpers/DatabaseManager.php',
            'src/Helpers/EnvironmentManager.php',
            'src/Events/EnvironmentSaved.php',
            'src/Events/LaravelInstallerFinished.php'
        ];
        
        foreach ($requiredFiles as $file) {
            if (!file_exists(__DIR__ . '/' . $file)) {
                return "Missing required file: {$file}";
            }
        }
        return true;
    },
    
    'Namespace Consistency' => function() {
        $files = glob(__DIR__ . '/src/**/*.php');
        foreach ($files as $file) {
            // Skip files that don't need namespaces
            $skipFiles = ['functions.php', 'web.php'];
            if (in_array(basename($file), $skipFiles)) {
                continue;
            }

            $content = file_get_contents($file);
            if (!preg_match('/namespace NeuronicLab\\\\Installer\\\\/', $content)) {
                return "Incorrect namespace in: " . basename($file);
            }
        }
        return true;
    },
    
    'No Rachidlaasri References' => function() {
        $files = array_merge(
            glob(__DIR__ . '/src/**/*.php'),
            [__DIR__ . '/composer.json']
        );
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            if (strpos($content, 'rachidlaasri') !== false || 
                strpos($content, 'RachidLaasri') !== false) {
                return "Found rachidlaasri reference in: " . basename($file);
            }
        }
        return true;
    },
    
    'Required Dependencies' => function() {
        $composer = json_decode(file_get_contents(__DIR__ . '/composer.json'), true);
        $required = ['php', 'laravel/framework', 'nesbot/carbon'];
        
        foreach ($required as $dep) {
            if (!isset($composer['require'][$dep])) {
                return "Missing required dependency: {$dep}";
            }
        }
        return true;
    },
    
    'Translation Keys' => function() {
        $translations = include __DIR__ . '/resources/lang/en/installer_messages.php';
        if (!isset($translations['final']['log_file_created'])) {
            return "Missing translation key: final.log_file_created";
        }
        return true;
    }
];

foreach ($manualTests as $testName => $testFunction) {
    $totalTests++;
    $result = $testFunction();
    
    if ($result === true) {
        echo "✅ {$testName}: PASSED\n";
        $passedTests++;
    } else {
        echo "❌ {$testName}: FAILED - {$result}\n";
        $failedTests++;
        $errors[] = [
            'category' => "Manual Test: {$testName}",
            'output' => $result
        ];
    }
}

// Calculate execution time
$endTime = microtime(true);
$executionTime = round($endTime - $startTime, 2);

// Display final results
echo "\n" . str_repeat("=", 60) . "\n";
echo "🏁 TEST RESULTS SUMMARY\n";
echo str_repeat("=", 60) . "\n";
echo "Total Tests: {$totalTests}\n";
echo "✅ Passed: {$passedTests}\n";
echo "❌ Failed: {$failedTests}\n";
echo "⏱️  Execution Time: {$executionTime} seconds\n\n";

if ($failedTests > 0) {
    echo "❌ FAILED TESTS DETAILS:\n";
    echo str_repeat("-", 40) . "\n";
    foreach ($errors as $error) {
        echo "Category: {$error['category']}\n";
        echo "Error: {$error['output']}\n\n";
    }
    echo "❌ Some tests failed. Please review and fix the issues above.\n";
    exit(1);
} else {
    echo "🎉 ALL TESTS PASSED!\n";
    echo "✨ NeuronicLab Installer is working correctly and is independent of rachidlaasri/laravel-installer.\n";
    echo "\n📋 Next Steps:\n";
    echo "1. Run 'composer update' to install dependencies\n";
    echo "2. Test the installer in a real Laravel application\n";
    echo "3. Consider releasing version 2.0.0\n";
    exit(0);
}

/**
 * Run manual tests when PHPUnit is not available
 */
function runManualTests($testFiles) {
    $results = ['total' => 0, 'passed' => 0, 'failed' => 0, 'errors' => []];
    
    foreach ($testFiles as $testFile) {
        echo "Running manual syntax check for: " . basename($testFile) . "\n";
        $output = shell_exec("php -l {$testFile} 2>&1");
        
        $results['total']++;
        if (strpos($output, 'No syntax errors') !== false) {
            $results['passed']++;
            echo "✅ Syntax OK\n";
        } else {
            $results['failed']++;
            $results['errors'][] = [
                'category' => 'Manual Test: ' . basename($testFile),
                'output' => $output
            ];
            echo "❌ Syntax Error\n";
        }
    }
    
    return $results;
}
