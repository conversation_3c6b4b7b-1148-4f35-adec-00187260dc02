<?php

/**
 * Simple Test Runner for NeuronicLab Installer
 * 
 * This script runs tests without requiring PHPUnit installation
 */

echo "🧪 NeuronicLab Installer - Simple Test Runner\n";
echo str_repeat("=", 50) . "\n\n";

// Mock PHPUnit TestCase for our tests
class MockTestCase {
    protected $testName = '';
    protected $assertions = 0;
    protected $failures = [];
    
    public function __construct($testName = '') {
        $this->testName = $testName;
    }
    
    protected function assertTrue($condition, $message = '') {
        $this->assertions++;
        if (!$condition) {
            $this->failures[] = $message ?: 'Assertion failed';
            throw new Exception($message ?: 'Assertion failed');
        }
    }
    
    protected function assertFalse($condition, $message = '') {
        $this->assertTrue(!$condition, $message);
    }
    
    protected function assertEquals($expected, $actual, $message = '') {
        $this->assertTrue($expected === $actual, 
            $message ?: "Expected '{$expected}', got '{$actual}'");
    }
    
    protected function assertFileExists($file, $message = '') {
        $this->assertTrue(file_exists($file), 
            $message ?: "File does not exist: {$file}");
    }
    
    protected function assertDirectoryExists($dir, $message = '') {
        $this->assertTrue(is_dir($dir), 
            $message ?: "Directory does not exist: {$dir}");
    }
    
    protected function assertArrayHasKey($key, $array, $message = '') {
        $this->assertTrue(array_key_exists($key, $array), 
            $message ?: "Array does not have key: {$key}");
    }
    
    protected function assertArrayNotHasKey($key, $array, $message = '') {
        $this->assertTrue(!array_key_exists($key, $array), 
            $message ?: "Array should not have key: {$key}");
    }
    
    protected function assertStringContainsString($needle, $haystack, $message = '') {
        $this->assertTrue(strpos($haystack, $needle) !== false, 
            $message ?: "String does not contain: {$needle}");
    }
    
    protected function assertStringNotContainsString($needle, $haystack, $message = '') {
        $this->assertTrue(strpos($haystack, $needle) === false, 
            $message ?: "String should not contain: {$needle}");
    }
    
    protected function assertIsArray($value, $message = '') {
        $this->assertTrue(is_array($value), 
            $message ?: "Value is not an array");
    }
}

// Test runner function
function runTestClass($className, $filePath) {
    echo "📋 Running: {$className}\n";
    echo str_repeat("-", 30) . "\n";
    
    // Include the test file
    require_once $filePath;
    
    // Get all methods that start with 'test_'
    $reflection = new ReflectionClass($className);
    $methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);
    
    $totalTests = 0;
    $passedTests = 0;
    $failedTests = 0;
    
    foreach ($methods as $method) {
        if (strpos($method->getName(), 'test_') === 0) {
            $totalTests++;
            $testName = $method->getName();
            
            try {
                $instance = new $className($testName);
                $method->invoke($instance);
                echo "✅ {$testName}: PASSED\n";
                $passedTests++;
            } catch (Exception $e) {
                echo "❌ {$testName}: FAILED - " . $e->getMessage() . "\n";
                $failedTests++;
            }
        }
    }
    
    echo "\nResults: {$passedTests}/{$totalTests} passed\n\n";
    
    return [
        'total' => $totalTests,
        'passed' => $passedTests,
        'failed' => $failedTests
    ];
}

// Replace the PHPUnit namespace in our test files temporarily
$testFiles = [
    'tests/PackageTest.php' => 'NeuronicLab\\Installer\\Tests\\PackageTest',
    'tests/HelpersTest.php' => 'NeuronicLab\\Installer\\Tests\\HelpersTest',
    'tests/ControllersTest.php' => 'NeuronicLab\\Installer\\Tests\\ControllersTest',
    'tests/IndependenceTest.php' => 'NeuronicLab\\Installer\\Tests\\IndependenceTest'
];

$totalTests = 0;
$totalPassed = 0;
$totalFailed = 0;

foreach ($testFiles as $file => $className) {
    if (file_exists($file)) {
        // Create a temporary version of the test file with our mock TestCase
        $content = file_get_contents($file);
        $content = str_replace('use PHPUnit\\Framework\\TestCase;', '', $content);
        $content = str_replace('extends TestCase', 'extends MockTestCase', $content);

        // Add the MockTestCase class to the temp file
        $mockTestCaseCode = '
class MockTestCase {
    protected $testName = "";
    protected $assertions = 0;
    protected $failures = [];

    public function __construct($testName = "") {
        $this->testName = $testName;
    }

    protected function assertTrue($condition, $message = "") {
        $this->assertions++;
        if (!$condition) {
            $this->failures[] = $message ?: "Assertion failed";
            throw new Exception($message ?: "Assertion failed");
        }
    }

    protected function assertFalse($condition, $message = "") {
        $this->assertTrue(!$condition, $message);
    }

    protected function assertEquals($expected, $actual, $message = "") {
        $this->assertTrue($expected === $actual,
            $message ?: "Expected \'{$expected}\', got \'{$actual}\'");
    }

    protected function assertFileExists($file, $message = "") {
        $this->assertTrue(file_exists($file),
            $message ?: "File does not exist: {$file}");
    }

    protected function assertDirectoryExists($dir, $message = "") {
        $this->assertTrue(is_dir($dir),
            $message ?: "Directory does not exist: {$dir}");
    }

    protected function assertArrayHasKey($key, $array, $message = "") {
        $this->assertTrue(array_key_exists($key, $array),
            $message ?: "Array does not have key: {$key}");
    }

    protected function assertArrayNotHasKey($key, $array, $message = "") {
        $this->assertTrue(!array_key_exists($key, $array),
            $message ?: "Array should not have key: {$key}");
    }

    protected function assertStringContainsString($needle, $haystack, $message = "") {
        $this->assertTrue(strpos($haystack, $needle) !== false,
            $message ?: "String does not contain: {$needle}");
    }

    protected function assertStringNotContainsString($needle, $haystack, $message = "") {
        $this->assertTrue(strpos($haystack, $needle) === false,
            $message ?: "String should not contain: {$needle}");
    }

    protected function assertIsArray($value, $message = "") {
        $this->assertTrue(is_array($value),
            $message ?: "Value is not an array");
    }
}
';

        $content = str_replace('<?php', '<?php' . $mockTestCaseCode, $content);

        $tempFile = sys_get_temp_dir() . '/' . basename($file);
        file_put_contents($tempFile, $content);
        
        $results = runTestClass($className, $tempFile);
        $totalTests += $results['total'];
        $totalPassed += $results['passed'];
        $totalFailed += $results['failed'];
        
        // Clean up temp file
        unlink($tempFile);
    }
}

echo str_repeat("=", 50) . "\n";
echo "🏁 FINAL RESULTS\n";
echo str_repeat("=", 50) . "\n";
echo "Total Tests: {$totalTests}\n";
echo "✅ Passed: {$totalPassed}\n";
echo "❌ Failed: {$totalFailed}\n";

if ($totalFailed > 0) {
    echo "\n❌ Some tests failed.\n";
    exit(1);
} else {
    echo "\n🎉 ALL TESTS PASSED!\n";
    exit(0);
}
