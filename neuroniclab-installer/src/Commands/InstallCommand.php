<?php

namespace NeuronicLab\Installer\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class InstallCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'neuroniclab:install {--force : Overwrite existing files}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Install NeuronicLab Installer package';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Installing NeuronicLab Installer...');

        // Publish configuration
        $this->call('vendor:publish', [
            '--provider' => 'NeuronicLab\Installer\Providers\InstallerServiceProvider',
            '--tag' => 'neuroniclab-installer-config',
            '--force' => $this->option('force')
        ]);

        // Publish assets
        $this->call('vendor:publish', [
            '--provider' => 'NeuronicLab\Installer\Providers\InstallerServiceProvider',
            '--tag' => 'neuroniclab-installer-assets',
            '--force' => $this->option('force')
        ]);

        // Publish views
        $this->call('vendor:publish', [
            '--provider' => 'NeuronicLab\Installer\Providers\InstallerServiceProvider',
            '--tag' => 'neuroniclab-installer-views',
            '--force' => $this->option('force')
        ]);

        // Publish database schema
        $this->call('vendor:publish', [
            '--provider' => 'NeuronicLab\Installer\Providers\InstallerServiceProvider',
            '--tag' => 'neuroniclab-installer-database',
            '--force' => $this->option('force')
        ]);

        // Publish language files
        $this->call('vendor:publish', [
            '--provider' => 'NeuronicLab\Installer\Providers\InstallerServiceProvider',
            '--tag' => 'neuroniclab-installer-lang',
            '--force' => $this->option('force')
        ]);

        $this->info('NeuronicLab Installer has been installed successfully!');
        $this->info('You can now access the installer at: /install');
        $this->info('Configure your settings in: config/neuroniclab-installer.php');

        return 0;
    }
}
