<?php

namespace NeuronicLab\Installer\Commands;

use Illuminate\Console\Command;

class PublishCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'neuroniclab:publish {--tag=* : Specific tags to publish} {--force : Overwrite existing files}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Publish NeuronicLab Installer assets and configuration';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $tags = $this->option('tag');
        $force = $this->option('force');

        if (empty($tags)) {
            // Publish all
            $this->call('vendor:publish', [
                '--provider' => 'NeuronicLab\Installer\Providers\InstallerServiceProvider',
                '--force' => $force
            ]);
            $this->info('All NeuronicLab Installer assets have been published!');
        } else {
            // Publish specific tags
            foreach ($tags as $tag) {
                $this->call('vendor:publish', [
                    '--provider' => 'NeuronicLab\Installer\Providers\InstallerServiceProvider',
                    '--tag' => "neuroniclab-installer-{$tag}",
                    '--force' => $force
                ]);
                $this->info("Published: {$tag}");
            }
        }

        return 0;
    }
}
