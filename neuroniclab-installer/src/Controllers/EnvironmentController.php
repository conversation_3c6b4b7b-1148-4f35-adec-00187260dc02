<?php

namespace NeuronicLab\Installer\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use NeuronicLab\Installer\Events\EnvironmentSaved;
use NeuronicLab\Installer\Helpers\EnvironmentManager;
use Validator;

class EnvironmentController extends Controller
{
    /**
     * @var EnvironmentManager
     */
    protected $EnvironmentManager;

    /**
     * @param EnvironmentManager $environmentManager
     */
    public function __construct(EnvironmentManager $environmentManager)
    {
        $this->EnvironmentManager = $environmentManager;
    }

    /**
     * Display the Environment menu page.
     *
     * @return \Illuminate\View\View
     */
    public function environmentMenu()
    {
        return view('neuroniclab-installer::environment');
    }

    /**
     * Display the Environment wizard page.
     *
     * @return \Illuminate\View\View
     */
    public function environmentWizard()
    {
        if (!$this->isLicenseVerified()) {
            Session::flash('license_error', 'Please, verify your license first!');
            return redirect()->route('NeuronicLabInstaller::license');
        }
        
        $envConfig = $this->EnvironmentManager->getEnvContent();
        return view('neuroniclab-installer::environment-wizard', compact('envConfig'));
    }

    /**
     * Display the Environment classic page.
     *
     * @return \Illuminate\View\View
     */
    public function environmentClassic()
    {
        $envConfig = $this->EnvironmentManager->getEnvContent();
        return view('neuroniclab-installer::environment-classic', compact('envConfig'));
    }

    /**
     * Processes the newly saved environment configuration (Classic).
     *
     * @param Request $input
     * @param Redirector $redirect
     * @return \Illuminate\Http\RedirectResponse
     */
    public function saveClassic(Request $input, Redirector $redirect)
    {
        $message = $this->EnvironmentManager->saveFileClassic($input);
        event(new EnvironmentSaved($input));

        return $redirect->route('NeuronicLabInstaller::environmentClassic')
                        ->with(['message' => $message]);
    }

    /**
     * Processes the newly saved environment configuration (Form Wizard).
     *
     * @param Request $request
     * @param Redirector $redirect
     * @return \Illuminate\Http\RedirectResponse
     */
    public function saveWizard(Request $request, Redirector $redirect)
    {
        if (!$this->isLicenseVerified()) {
            Session::flash('license_error', 'Please, verify your license first!');
            return redirect()->route('NeuronicLabInstaller::license');
        }

        $rules = config('neuroniclab-installer.environment.form.rules');
        $messages = [
            'environment_custom.required_if' => trans('neuroniclab-installer::installer_messages.environment.wizard.form.name_required'),
        ];

        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            return $redirect->route('NeuronicLabInstaller::environmentWizard')->withInput()->withErrors($validator->errors());
        }

        if (!$this->checkDatabaseConnection($request)) {
            return $redirect->route('NeuronicLabInstaller::environmentWizard')->withInput()->withErrors([
                'database_connection' => trans('neuroniclab-installer::installer_messages.environment.wizard.form.db_connection_failed'),
            ]);
        }

        $results = $this->EnvironmentManager->saveFileWizard($request);
        event(new EnvironmentSaved($request));

        // Import database schema
        $this->importDatabaseSchema($request);

        return $redirect->route('NeuronicLabInstaller::database')
                        ->with(['results' => $results]);
    }

    /**
     * Check if license is verified.
     *
     * @return bool
     */
    protected function isLicenseVerified()
    {
        $licenseFile = config('neuroniclab-installer.license.verification_file', 'vendor/mockery/mockery/verified');
        return file_exists(base_path($licenseFile));
    }

    /**
     * Check database connection.
     *
     * @param Request $request
     * @return bool
     */
    private function checkDatabaseConnection(Request $request)
    {
        $connection = $request->input('database_connection');
        $settings = config("database.connections.{$connection}");

        config([
            'database' => [
                'default' => $connection,
                'connections' => [
                    $connection => array_merge($settings, [
                        'driver' => $connection,
                        'host' => $request->input('database_hostname'),
                        'port' => $request->input('database_port'),
                        'database' => $request->input('database_name'),
                        'username' => $request->input('database_username'),
                        'password' => $request->input('database_password'),
                    ]),
                ],
            ],
        ]);

        try {
            DB::connection()->getPdo();
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Import database schema.
     *
     * @param Request $request
     * @return void
     */
    protected function importDatabaseSchema(Request $request)
    {
        // Increase memory limit and execution time for SQL import
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 300);

        $db_host = $request->input('database_hostname');
        $db_user = $request->input('database_username');
        $db_pass = $request->input('database_password');
        $db_name = $request->input('database_name');

        $sqlFile = config('neuroniclab-installer.database.schema_file', database_path('neuroniclab-installer/schema.sql'));

        // Try MySQL command line first for better performance
        if ($this->importViaMysqlCommand($db_host, $db_user, $db_pass, $db_name, $sqlFile)) {
            return;
        }

        // Fallback to PHP method
        $this->importViaPHP($sqlFile);
    }

    /**
     * Import database via MySQL command line.
     *
     * @param string $host
     * @param string $user
     * @param string $password
     * @param string $database
     * @param string $sqlFile
     * @return bool
     */
    protected function importViaMysqlCommand($host, $user, $password, $database, $sqlFile)
    {
        if (!file_exists($sqlFile)) {
            Log::error("SQL file not found: {$sqlFile}");
            return false;
        }

        $mysqlPath = config('neuroniclab-installer.database.mysql_path', 'mysql');
        $command = "{$mysqlPath} -h {$host} -u {$user} -p'{$password}' {$database} < {$sqlFile}";

        try {
            $output = shell_exec($command . ' 2>&1');
            if ($output && strpos($output, 'ERROR') !== false) {
                Log::error('SQL import failed: ' . $output);
                return false;
            }
            return true;
        } catch (Exception $e) {
            Log::error('SQL import exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Import database via PHP.
     *
     * @param string $sqlFile
     * @return void
     */
    protected function importViaPHP($sqlFile)
    {
        if (!file_exists($sqlFile)) {
            Log::error("SQL file not found: {$sqlFile}");
            return;
        }

        try {
            $sql = file_get_contents($sqlFile);
            $statements = array_filter(array_map('trim', explode(';', $sql)));

            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    DB::unprepared($statement);
                }
            }
        } catch (Exception $e) {
            Log::error('PHP SQL import failed: ' . $e->getMessage());
            throw $e;
        }
    }
}
