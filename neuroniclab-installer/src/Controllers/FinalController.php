<?php

namespace NeuronicLab\Installer\Controllers;

use Illuminate\Routing\Controller;
use NeuronicLab\Installer\Events\LaravelInstallerFinished;
use NeuronicLab\Installer\Helpers\EnvironmentManager;
use NeuronicLab\Installer\Helpers\FinalInstallManager;
use NeuronicLab\Installer\Helpers\InstalledFileManager;

class FinalController extends Controller
{
    /**
     * Update installed file and display finished view.
     *
     * @param \NeuronicLab\Installer\Helpers\InstalledFileManager $fileManager
     * @param \NeuronicLab\Installer\Helpers\FinalInstallManager $finalInstall
     * @param \NeuronicLab\Installer\Helpers\EnvironmentManager $environment
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function finish(InstalledFileManager $fileManager, FinalInstallManager $finalInstall, EnvironmentManager $environment)
    {
        $finalMessages = $finalInstall->runFinal();
        $finalStatusMessage = $fileManager->update();
        $finalEnvFile = $environment->getEnvContent();

        event(new LaravelInstallerFinished);

        return view('neuroniclab-installer::finished', compact('finalMessages', 'finalStatusMessage', 'finalEnvFile'));
    }
}
