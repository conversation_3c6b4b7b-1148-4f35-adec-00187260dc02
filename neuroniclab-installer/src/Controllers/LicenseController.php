<?php

namespace NeuronicLab\Installer\Controllers;

use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class LicenseController extends Controller
{
    /**
     * Display the license verification page.
     *
     * @return \Illuminate\View\View
     */
    public function license()
    {
        return view('neuroniclab-installer::license');
    }

    /**
     * Process license verification.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function licenseCheck(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'username' => 'required|string',
            'purchase_code' => 'required|string'
        ]);

        $config = config('neuroniclab-installer.license');
        
        // If license verification is disabled, just create the verification file
        if (!$config['enabled']) {
            $this->createVerificationFile();
            Session::flash('license_success', 'License verified successfully!');
            return redirect()->route('NeuronicLabInstaller::environmentWizard');
        }

        // Perform actual license verification if enabled
        if ($this->verifyLicense($request)) {
            $this->createVerificationFile();
            Session::flash('license_success', 'License verified successfully!');
            return redirect()->route('NeuronicLabInstaller::environmentWizard');
        } else {
            Session::flash('license_error', 'License verification failed. Please check your credentials.');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Verify license with remote server.
     *
     * @param Request $request
     * @return bool
     */
    protected function verifyLicense(Request $request)
    {
        try {
            $config = config('neuroniclab-installer.license');
            
            if (!$config['verification_url']) {
                // If no verification URL is set, just return true
                return true;
            }

            $client = new Client();
            $response = $client->post($config['verification_url'], [
                'form_params' => [
                    'email' => $request->input('email'),
                    'username' => $request->input('username'),
                    'purchase_code' => $request->input('purchase_code'),
                    'item_id' => $config['item_id'],
                    'item_name' => $config['item_name'],
                    'domain' => URL::to('/'),
                ]
            ]);

            $result = json_decode($response->getBody(), true);
            return isset($result['status']) && $result['status'] === 'success';
            
        } catch (\Exception $e) {
            Log::error('License verification failed: ' . $e->getMessage());
            
            // If verification fails due to network issues, allow installation in development
            if (app()->environment('local')) {
                return true;
            }
            
            return false;
        }
    }

    /**
     * Create verification file.
     *
     * @return void
     */
    protected function createVerificationFile()
    {
        $verificationPath = base_path(config('neuroniclab-installer.license.verification_file'));
        
        // Ensure directory exists
        $directory = dirname($verificationPath);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        $handle = fopen($verificationPath, "w");
        if ($handle) {
            fwrite($handle, json_encode([
                'verified' => true,
                'timestamp' => time(),
                'domain' => URL::to('/'),
            ]));
            fclose($handle);
        }
    }

    /**
     * Check if license is already verified.
     *
     * @return bool
     */
    public static function isLicenseVerified()
    {
        $verificationFile = base_path(config('neuroniclab-installer.license.verification_file'));
        return file_exists($verificationFile);
    }
}
