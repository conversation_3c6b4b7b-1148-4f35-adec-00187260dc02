<?php

namespace NeuronicLab\Installer\Controllers;

use Illuminate\Routing\Controller;
use NeuronicLab\Installer\Helpers\PermissionsChecker;

class PermissionsController extends Controller
{
    /**
     * @var PermissionsChecker
     */
    protected $permissions;

    /**
     * @param PermissionsChecker $checker
     */
    public function __construct(PermissionsChecker $checker)
    {
        $this->permissions = $checker;
    }

    /**
     * Display the permissions check page.
     *
     * @return \Illuminate\View\View
     */
    public function permissions()
    {
        $permissions = $this->permissions->check(
            config('neuroniclab-installer.permissions')
        );

        return view('neuroniclab-installer::permissions', compact('permissions'));
    }
}
