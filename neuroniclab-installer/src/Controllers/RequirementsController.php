<?php

namespace NeuronicLab\Installer\Controllers;

use Illuminate\Routing\Controller;
use NeuronicLab\Installer\Helpers\RequirementsChecker;

class RequirementsController extends Controller
{
    /**
     * @var RequirementsChecker
     */
    protected $requirements;

    /**
     * @param RequirementsChecker $checker
     */
    public function __construct(RequirementsChecker $checker)
    {
        $this->requirements = $checker;
    }

    /**
     * Display the requirements page.
     *
     * @return \Illuminate\View\View
     */
    public function requirements()
    {
        $phpSupportInfo = $this->requirements->checkPHPversion(
            config('neuroniclab-installer.core.minPhpVersion')
        );
        
        $requirements = $this->requirements->check(
            config('neuroniclab-installer.requirements')
        );

        return view('neuroniclab-installer::requirements', compact('requirements', 'phpSupportInfo'));
    }
}
