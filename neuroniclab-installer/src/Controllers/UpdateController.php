<?php

namespace NeuronicLab\Installer\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class UpdateController extends Controller
{
    /**
     * Display the update page.
     *
     * @return \Illuminate\View\View
     */
    public function welcome()
    {
        return view('neuroniclab-installer::update.welcome');
    }

    /**
     * Display the update overview page.
     *
     * @return \Illuminate\View\View
     */
    public function overview()
    {
        return view('neuroniclab-installer::update.overview');
    }

    /**
     * Migrate and seed the database.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function database()
    {
        return redirect()->route('neuroniclab-installer.update.final');
    }

    /**
     * Update completed.
     *
     * @return \Illuminate\View\View
     */
    public function finished()
    {
        return view('neuroniclab-installer::update.finished');
    }
}
