<?php

namespace NeuronicLab\Installer\Helpers;

use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DatabaseManager
{
    /**
     * Migrate and seed the database.
     *
     * @return array
     */
    public function migrateAndSeed()
    {
        $outputLog = [];

        try {
            $this->sqlite($outputLog);
        } catch (Exception $e) {
            return $this->response($e->getMessage(), 'error', $outputLog);
        }

        return $this->migrate($outputLog);
    }

    /**
     * Run the migration and call the seeder.
     *
     * @param array $outputLog
     * @return array
     */
    private function migrate($outputLog = [])
    {
        try {
            Artisan::call('migrate', ['--force' => true]);
            $outputLog[] = Artisan::output();
        } catch (Exception $e) {
            return $this->response($e->getMessage(), 'error', $outputLog);
        }

        return $this->seed($outputLog);
    }

    /**
     * Seed the database.
     *
     * @param array $outputLog
     * @return array
     */
    private function seed($outputLog = [])
    {
        try {
            Artisan::call('db:seed', ['--force' => true]);
            $outputLog[] = Artisan::output();
        } catch (Exception $e) {
            return $this->response($e->getMessage(), 'error', $outputLog);
        }

        return $this->response(trans('neuroniclab-installer::installer_messages.final.finished'), 'success', $outputLog);
    }

    /**
     * Return a formatted error messages.
     *
     * @param string $message
     * @param string $status
     * @param array $outputLog
     * @return array
     */
    private function response($message, $status = 'danger', $outputLog = [])
    {
        return [
            'status' => $status,
            'message' => $message,
            'dbOutputLog' => $outputLog,
        ];
    }

    /**
     * Check if the database is SQLite.
     *
     * @param array $outputLog
     * @return void
     * @throws Exception
     */
    private function sqlite($outputLog = [])
    {
        if (DB::connection() instanceof \Illuminate\Database\SQLiteConnection) {
            $database = DB::connection()->getDatabaseName();
            if (!file_exists($database)) {
                throw new Exception('SQLite database file does not exist: ' . $database);
            }
        }
    }
}
