<?php

namespace NeuronicLab\Installer\Helpers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;

class EnvironmentManager
{
    /**
     * @var string
     */
    private $envPath;

    /**
     * @var string
     */
    private $envExamplePath;

    /**
     * Set the .env and .env.example paths.
     */
    public function __construct()
    {
        $this->envPath = base_path('.env');
        $this->envExamplePath = base_path('.env.example');
    }

    /**
     * Get the content of the .env file.
     *
     * @return string
     */
    public function getEnvContent()
    {
        if (!file_exists($this->envPath)) {
            if (file_exists($this->envExamplePath)) {
                return file_get_contents($this->envExamplePath);
            }
            return '';
        }

        return file_get_contents($this->envPath);
    }

    /**
     * Save the form content to the .env file.
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    public function saveFileWizard(Request $request)
    {
        $results = trans('neuroniclab-installer::installer_messages.environment.success');

        $envFileData = 
            'APP_NAME=\'' . $request->app_name . "'\n" .
            'APP_ENV=' . $request->environment . "\n" .
            'APP_KEY=' . 'base64:' . base64_encode(random_bytes(32)) . "\n" .
            'APP_DEBUG=' . $request->app_debug . "\n" .
            'APP_URL=' . $request->app_url . "\n\n" .
            'LOG_CHANNEL=stack' . "\n" .
            'LOG_DEPRECATIONS_CHANNEL=null' . "\n" .
            'LOG_LEVEL=debug' . "\n\n" .
            'DB_CONNECTION=' . $request->database_connection . "\n" .
            'DB_HOST=' . $request->database_hostname . "\n" .
            'DB_PORT=' . $request->database_port . "\n" .
            'DB_DATABASE=' . $request->database_name . "\n" .
            'DB_USERNAME=' . $request->database_username . "\n" .
            'DB_PASSWORD=' . $request->database_password . "\n\n" .
            'BROADCAST_DRIVER=log' . "\n" .
            'CACHE_DRIVER=file' . "\n" .
            'FILESYSTEM_DISK=local' . "\n" .
            'QUEUE_CONNECTION=sync' . "\n" .
            'SESSION_DRIVER=file' . "\n" .
            'SESSION_LIFETIME=120' . "\n\n" .
            'MEMCACHED_HOST=127.0.0.1' . "\n\n" .
            'REDIS_HOST=127.0.0.1' . "\n" .
            'REDIS_PASSWORD=null' . "\n" .
            'REDIS_PORT=6379' . "\n\n" .
            'MAIL_MAILER=smtp' . "\n" .
            'MAIL_HOST=mailpit' . "\n" .
            'MAIL_PORT=1025' . "\n" .
            'MAIL_USERNAME=null' . "\n" .
            'MAIL_PASSWORD=null' . "\n" .
            'MAIL_ENCRYPTION=null' . "\n" .
            'MAIL_FROM_ADDRESS="<EMAIL>"' . "\n" .
            'MAIL_FROM_NAME="${APP_NAME}"' . "\n\n" .
            'AWS_ACCESS_KEY_ID=' . "\n" .
            'AWS_SECRET_ACCESS_KEY=' . "\n" .
            'AWS_DEFAULT_REGION=us-east-1' . "\n" .
            'AWS_BUCKET=' . "\n" .
            'AWS_USE_PATH_STYLE_ENDPOINT=false' . "\n\n" .
            'PUSHER_APP_ID=' . "\n" .
            'PUSHER_APP_KEY=' . "\n" .
            'PUSHER_APP_SECRET=' . "\n" .
            'PUSHER_HOST=' . "\n" .
            'PUSHER_PORT=443' . "\n" .
            'PUSHER_SCHEME=https' . "\n" .
            'PUSHER_APP_CLUSTER=mt1' . "\n\n" .
            'VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"' . "\n" .
            'VITE_PUSHER_HOST="${PUSHER_HOST}"' . "\n" .
            'VITE_PUSHER_PORT="${PUSHER_PORT}"' . "\n" .
            'VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"' . "\n" .
            'VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"' . "\n";

        try {
            file_put_contents($this->envPath, $envFileData);
        } catch (Exception $e) {
            $results = trans('neuroniclab-installer::installer_messages.environment.errors');
        }

        return $results;
    }

    /**
     * Save the form content to the .env file (classic mode).
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    public function saveFileClassic(Request $request)
    {
        $message = trans('neuroniclab-installer::installer_messages.environment.success');

        try {
            file_put_contents($this->envPath, $request->envConfig);
        } catch (Exception $e) {
            $message = trans('neuroniclab-installer::installer_messages.environment.errors');
        }

        return $message;
    }
}
