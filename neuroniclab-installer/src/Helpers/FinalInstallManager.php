<?php

namespace NeuronicLab\Installer\Helpers;

use Exception;
use Illuminate\Support\Facades\Artisan;

class FinalInstallManager
{
    /**
     * Run final commands.
     *
     * @return array
     */
    public function runFinal()
    {
        $finalMessages = [];

        try {
            // Generate application key
            Artisan::call('key:generate', ['--force' => true]);
            $finalMessages[] = [
                'message' => 'Application key generated successfully.',
                'status' => 'success'
            ];
        } catch (Exception $e) {
            $finalMessages[] = [
                'message' => 'Failed to generate application key: ' . $e->getMessage(),
                'status' => 'error'
            ];
        }

        try {
            // Clear configuration cache
            Artisan::call('config:clear');
            $finalMessages[] = [
                'message' => 'Configuration cache cleared.',
                'status' => 'success'
            ];
        } catch (Exception $e) {
            $finalMessages[] = [
                'message' => 'Failed to clear configuration cache: ' . $e->getMessage(),
                'status' => 'error'
            ];
        }

        try {
            // Clear route cache
            Artisan::call('route:clear');
            $finalMessages[] = [
                'message' => 'Route cache cleared.',
                'status' => 'success'
            ];
        } catch (Exception $e) {
            $finalMessages[] = [
                'message' => 'Failed to clear route cache: ' . $e->getMessage(),
                'status' => 'error'
            ];
        }

        try {
            // Clear view cache
            Artisan::call('view:clear');
            $finalMessages[] = [
                'message' => 'View cache cleared.',
                'status' => 'success'
            ];
        } catch (Exception $e) {
            $finalMessages[] = [
                'message' => 'Failed to clear view cache: ' . $e->getMessage(),
                'status' => 'error'
            ];
        }

        try {
            // Optimize for production if not in debug mode
            if (!config('app.debug', false)) {
                Artisan::call('config:cache');
                Artisan::call('route:cache');
                Artisan::call('view:cache');
                
                $finalMessages[] = [
                    'message' => 'Application optimized for production.',
                    'status' => 'success'
                ];
            }
        } catch (Exception $e) {
            $finalMessages[] = [
                'message' => 'Failed to optimize application: ' . $e->getMessage(),
                'status' => 'warning'
            ];
        }

        return $finalMessages;
    }
}
