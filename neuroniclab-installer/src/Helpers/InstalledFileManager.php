<?php

namespace NeuronicLab\Installer\Helpers;

use Carbon\Carbon;

class InstalledFileManager
{
    /**
     * Create installed file.
     *
     * @return string
     */
    public function update()
    {
        $installedLogFile = storage_path('installed');

        $dateStamp = Carbon::now()->format('Y/m/d h:i:sa');
        
        if (!file_exists($installedLogFile)) {
            $message = trans('neuroniclab-installer::installer_messages.final.log_file_created', ['date' => $dateStamp]);
        } else {
            $message = trans('neuroniclab-installer::installer_messages.final.log_file_updated', ['date' => $dateStamp]);
        }

        file_put_contents($installedLogFile, $dateStamp . "\n", FILE_APPEND | LOCK_EX);

        return $message;
    }

    /**
     * Check if the application is already installed.
     *
     * @return bool
     */
    public function alreadyInstalled()
    {
        return file_exists(storage_path('installed'));
    }
}
