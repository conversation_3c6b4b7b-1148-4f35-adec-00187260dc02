<?php

if (!function_exists('isActive')) {
    /**
     * Set the active class to the current opened menu.
     *
     * @param  string  $route
     * @param  string  $output
     * @return string
     */
    function isActive($route, $output = "active")
    {
        if (Route::currentRouteName() == $route) {
            return $output;
        }
    }
}

if (!function_exists('isActiveUrl')) {
    /**
     * Set the active class to the current opened menu.
     *
     * @param  string  $url
     * @param  string  $output
     * @return string
     */
    function isActiveUrl($url, $output = "active")
    {
        if (Request::is($url)) {
            return $output;
        }
    }
}
