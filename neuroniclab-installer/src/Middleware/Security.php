<?php

namespace NeuronicLab\Installer\Middleware;

use Closure;
use Illuminate\Support\Facades\Session;

class Security
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Skip security checks during installation
        if ($request->is('install') || $request->is('install/*')) {
            return $next($request);
        }

        // Skip security checks in development environment or if verification file exists
        $licenseFile = config('neuroniclab-installer.license.verification_file', 'vendor/mockery/mockery/verified');
        if (app()->environment('local') || file_exists(base_path($licenseFile))) {
            return $next($request);
        }

        $securityDataSession = Session::get('securityData');

        if ($securityDataSession && ($securityDataSession['status'] == 'not_verified' || $securityDataSession['status'] == 'multiple_domain')) {
            if ($request->is('admin')) {
                return $next($request);
            }
            return redirect()->route('back.dashboard');
        }

        return $next($request);
    }
}
