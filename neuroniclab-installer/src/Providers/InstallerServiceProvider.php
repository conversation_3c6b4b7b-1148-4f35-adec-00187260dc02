<?php

namespace NeuronicLab\Installer\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Routing\Router;
use NeuronicLab\Installer\Middleware\CanInstall;
use NeuronicLab\Installer\Middleware\Security;
use NeuronicLab\Installer\Commands\InstallCommand;
use NeuronicLab\Installer\Commands\PublishCommand;

class InstallerServiceProvider extends ServiceProvider
{
    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->mergeConfigFrom(
            __DIR__.'/../../config/neuroniclab-installer.php',
            'neuroniclab-installer'
        );

        // Register commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                InstallCommand::class,
                PublishCommand::class,
            ]);
        }
    }

    /**
     * Bootstrap the application events.
     *
     * @param \Illuminate\Routing\Router $router
     */
    public function boot(Router $router)
    {
        $this->loadRoutesFrom(__DIR__.'/../Routes/web.php');
        $this->loadViewsFrom(__DIR__.'/../../resources/views', 'neuroniclab-installer');
        $this->loadTranslationsFrom(__DIR__.'/../../resources/lang', 'neuroniclab-installer');

        // Register middleware
        $router->middlewareGroup('neuroniclab-install', [CanInstall::class]);
        $router->middlewareGroup('neuroniclab-security', [Security::class]);

        $this->publishes([
            __DIR__.'/../../config/neuroniclab-installer.php' => config_path('neuroniclab-installer.php'),
        ], 'neuroniclab-installer-config');

        $this->publishes([
            __DIR__.'/../../resources/views' => resource_path('views/vendor/neuroniclab-installer'),
        ], 'neuroniclab-installer-views');

        $this->publishes([
            __DIR__.'/../../assets' => public_path('neuroniclab-installer'),
        ], 'neuroniclab-installer-assets');

        $this->publishes([
            __DIR__.'/../../database' => database_path('neuroniclab-installer'),
        ], 'neuroniclab-installer-database');

        $this->publishes([
            __DIR__.'/../../resources/lang' => resource_path('lang/vendor/neuroniclab-installer'),
        ], 'neuroniclab-installer-lang');

        // Publish all at once
        $this->publishes([
            __DIR__.'/../../config/neuroniclab-installer.php' => config_path('neuroniclab-installer.php'),
            __DIR__.'/../../resources/views' => resource_path('views/vendor/neuroniclab-installer'),
            __DIR__.'/../../assets' => public_path('neuroniclab-installer'),
            __DIR__.'/../../database' => database_path('neuroniclab-installer'),
            __DIR__.'/../../resources/lang' => resource_path('lang/vendor/neuroniclab-installer'),
        ], 'neuroniclab-installer');
    }
}
