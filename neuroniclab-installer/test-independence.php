<?php

/**
 * Test script to verify that neuroniclab-installer works independently
 * without rachidlaasri/laravel-installer dependency
 */

echo "Testing NeuronicLab Installer Independence...\n\n";

$errors = [];
$success = [];

// Test 1: Check if rachidlaasri classes are no longer referenced
echo "1. Checking for rachidlaasri references...\n";
$files = [
    'src/Controllers/DatabaseController.php',
    'src/Controllers/EnvironmentController.php',
    'src/Controllers/FinalController.php',
    'src/Controllers/PermissionsController.php',
    'src/Controllers/RequirementsController.php',
];

foreach ($files as $file) {
    $content = file_get_contents(__DIR__ . '/' . $file);
    if (strpos($content, 'RachidLaasri\\LaravelInstaller') !== false) {
        $errors[] = "Found rachidlaasri reference in {$file}";
    } else {
        $success[] = "No rachidlaasri references in {$file}";
    }
}

// Test 2: Check if composer.json no longer has the dependency
echo "\n2. Checking composer.json...\n";
$composer = json_decode(file_get_contents(__DIR__ . '/composer.json'), true);
if (isset($composer['require']['rachidlaasri/laravel-installer'])) {
    $errors[] = "rachidlaasri/laravel-installer still in composer.json";
} else {
    $success[] = "rachidlaasri/laravel-installer removed from composer.json";
}

// Test 3: Check if all new helper classes exist
echo "\n3. Checking new helper classes...\n";
$helperClasses = [
    'src/Helpers/DatabaseManager.php',
    'src/Helpers/EnvironmentManager.php',
    'src/Helpers/FinalInstallManager.php',
    'src/Helpers/InstalledFileManager.php',
    'src/Helpers/PermissionsChecker.php',
    'src/Helpers/RequirementsChecker.php',
];

foreach ($helperClasses as $class) {
    if (file_exists(__DIR__ . '/' . $class)) {
        $success[] = "Helper class exists: {$class}";
    } else {
        $errors[] = "Missing helper class: {$class}";
    }
}

// Test 4: Check if event classes exist
echo "\n4. Checking event classes...\n";
$eventClasses = [
    'src/Events/EnvironmentSaved.php',
    'src/Events/LaravelInstallerFinished.php',
];

foreach ($eventClasses as $class) {
    if (file_exists(__DIR__ . '/' . $class)) {
        $success[] = "Event class exists: {$class}";
    } else {
        $errors[] = "Missing event class: {$class}";
    }
}

// Test 5: Check PHP syntax of helper classes
echo "\n5. Testing PHP syntax...\n";
$helperFiles = [
    'src/Helpers/DatabaseManager.php',
    'src/Helpers/EnvironmentManager.php',
    'src/Helpers/FinalInstallManager.php',
    'src/Helpers/InstalledFileManager.php',
    'src/Helpers/PermissionsChecker.php',
    'src/Helpers/RequirementsChecker.php',
    'src/Events/EnvironmentSaved.php',
    'src/Events/LaravelInstallerFinished.php',
];

foreach ($helperFiles as $file) {
    $output = shell_exec("php -l " . __DIR__ . '/' . $file . " 2>&1");
    if (strpos($output, 'No syntax errors') !== false) {
        $success[] = "Valid PHP syntax: {$file}";
    } else {
        $errors[] = "Syntax error in {$file}: " . trim($output);
    }
}

// Results
echo "\n" . str_repeat("=", 50) . "\n";
echo "TEST RESULTS\n";
echo str_repeat("=", 50) . "\n";

if (!empty($success)) {
    echo "\n✅ SUCCESSES (" . count($success) . "):\n";
    foreach ($success as $item) {
        echo "  ✓ {$item}\n";
    }
}

if (!empty($errors)) {
    echo "\n❌ ERRORS (" . count($errors) . "):\n";
    foreach ($errors as $item) {
        echo "  ✗ {$item}\n";
    }
    echo "\nSome issues need to be resolved.\n";
    exit(1);
} else {
    echo "\n🎉 ALL TESTS PASSED!\n";
    echo "NeuronicLab Installer is now independent of rachidlaasri/laravel-installer.\n";
    exit(0);
}
