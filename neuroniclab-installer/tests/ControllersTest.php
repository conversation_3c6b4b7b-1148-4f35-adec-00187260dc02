<?php

namespace NeuronicLab\Installer\Tests;

use PHPUnit\Framework\TestCase;

class ControllersTest extends TestCase
{
    public function test_all_controller_classes_exist()
    {
        $controllerClasses = [
            'DatabaseController',
            'EnvironmentController',
            'FinalController',
            'LicenseController',
            'PermissionsController',
            'RequirementsController',
            'WelcomeController',
        ];

        foreach ($controllerClasses as $class) {
            $filePath = __DIR__ . "/../src/Controllers/{$class}.php";
            $this->assertFileExists($filePath, "Controller class {$class}.php does not exist");
        }
    }

    public function test_controllers_have_valid_php_syntax()
    {
        $controllerFiles = glob(__DIR__ . '/../src/Controllers/*.php');

        foreach ($controllerFiles as $file) {
            $output = shell_exec("php -l {$file} 2>&1");
            $this->assertStringContainsString('No syntax errors', $output, 
                "Syntax error in " . basename($file));
        }
    }

    public function test_controllers_have_correct_namespace()
    {
        $controllerFiles = glob(__DIR__ . '/../src/Controllers/*.php');

        foreach ($controllerFiles as $file) {
            $content = file_get_contents($file);
            $this->assertStringContainsString('namespace NeuronicLab\Installer\Controllers;', $content, 
                "Incorrect namespace in " . basename($file));
        }
    }

    public function test_no_rachidlaasri_references_in_controllers()
    {
        $controllerFiles = glob(__DIR__ . '/../src/Controllers/*.php');
        
        foreach ($controllerFiles as $file) {
            $content = file_get_contents($file);
            $this->assertStringNotContainsString('RachidLaasri\\LaravelInstaller', $content, 
                "Found rachidlaasri reference in " . basename($file));
        }
    }

    public function test_controllers_use_correct_helper_imports()
    {
        $expectedImports = [
            'DatabaseController.php' => [
                'use NeuronicLab\Installer\Helpers\DatabaseManager;'
            ],
            'EnvironmentController.php' => [
                'use NeuronicLab\Installer\Events\EnvironmentSaved;',
                'use NeuronicLab\Installer\Helpers\EnvironmentManager;'
            ],
            'FinalController.php' => [
                'use NeuronicLab\Installer\Events\LaravelInstallerFinished;',
                'use NeuronicLab\Installer\Helpers\EnvironmentManager;',
                'use NeuronicLab\Installer\Helpers\FinalInstallManager;',
                'use NeuronicLab\Installer\Helpers\InstalledFileManager;'
            ],
            'PermissionsController.php' => [
                'use NeuronicLab\Installer\Helpers\PermissionsChecker;'
            ],
            'RequirementsController.php' => [
                'use NeuronicLab\Installer\Helpers\RequirementsChecker;'
            ]
        ];

        foreach ($expectedImports as $fileName => $imports) {
            $filePath = __DIR__ . "/../src/Controllers/{$fileName}";
            $content = file_get_contents($filePath);
            
            foreach ($imports as $import) {
                $this->assertStringContainsString($import, $content, 
                    "Missing import '{$import}' in {$fileName}");
            }
        }
    }

    public function test_controllers_extend_base_controller()
    {
        $controllerFiles = glob(__DIR__ . '/../src/Controllers/*.php');

        foreach ($controllerFiles as $file) {
            $content = file_get_contents($file);
            $this->assertStringContainsString('extends Controller', $content, 
                "Controller " . basename($file) . " does not extend base Controller");
        }
    }

    public function test_database_controller_has_required_methods()
    {
        $filePath = __DIR__ . '/../src/Controllers/DatabaseController.php';
        $content = file_get_contents($filePath);
        
        $this->assertStringContainsString('function database(', $content, 
            "DatabaseController missing database() method");
    }

    public function test_environment_controller_has_required_methods()
    {
        $filePath = __DIR__ . '/../src/Controllers/EnvironmentController.php';
        $content = file_get_contents($filePath);
        
        $requiredMethods = [
            'environmentMenu',
            'environmentWizard',
            'environmentClassic',
            'saveClassic',
            'saveWizard'
        ];

        foreach ($requiredMethods as $method) {
            $this->assertStringContainsString("function {$method}(", $content, 
                "EnvironmentController missing {$method}() method");
        }
    }

    public function test_final_controller_has_required_methods()
    {
        $filePath = __DIR__ . '/../src/Controllers/FinalController.php';
        $content = file_get_contents($filePath);
        
        $this->assertStringContainsString('function finish(', $content, 
            "FinalController missing finish() method");
    }

    public function test_license_controller_has_required_methods()
    {
        $filePath = __DIR__ . '/../src/Controllers/LicenseController.php';
        $content = file_get_contents($filePath);
        
        $requiredMethods = [
            'license',
            'licenseCheck'
        ];

        foreach ($requiredMethods as $method) {
            $this->assertStringContainsString("function {$method}(", $content, 
                "LicenseController missing {$method}() method");
        }
    }

    public function test_permissions_controller_has_required_methods()
    {
        $filePath = __DIR__ . '/../src/Controllers/PermissionsController.php';
        $content = file_get_contents($filePath);
        
        $this->assertStringContainsString('function permissions(', $content, 
            "PermissionsController missing permissions() method");
    }

    public function test_requirements_controller_has_required_methods()
    {
        $filePath = __DIR__ . '/../src/Controllers/RequirementsController.php';
        $content = file_get_contents($filePath);
        
        $this->assertStringContainsString('function requirements(', $content, 
            "RequirementsController missing requirements() method");
    }

    public function test_welcome_controller_has_required_methods()
    {
        $filePath = __DIR__ . '/../src/Controllers/WelcomeController.php';
        $content = file_get_contents($filePath);
        
        $this->assertStringContainsString('function welcome(', $content, 
            "WelcomeController missing welcome() method");
    }
}
