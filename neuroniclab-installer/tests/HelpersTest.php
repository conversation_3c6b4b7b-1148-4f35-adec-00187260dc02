<?php

namespace NeuronicLab\Installer\Tests;

use PHPUnit\Framework\TestCase;

class HelpersTest extends TestCase
{
    public function test_all_helper_classes_exist()
    {
        $helperClasses = [
            'DatabaseManager',
            'EnvironmentManager',
            'FinalInstallManager',
            'InstalledFileManager',
            'PermissionsChecker',
            'RequirementsChecker',
        ];

        foreach ($helperClasses as $class) {
            $filePath = __DIR__ . "/../src/Helpers/{$class}.php";
            $this->assertFileExists($filePath, "Helper class {$class}.php does not exist");
        }
    }

    public function test_all_event_classes_exist()
    {
        $eventClasses = [
            'EnvironmentSaved',
            'LaravelInstallerFinished',
        ];

        foreach ($eventClasses as $class) {
            $filePath = __DIR__ . "/../src/Events/{$class}.php";
            $this->assertFileExists($filePath, "Event class {$class}.php does not exist");
        }
    }

    public function test_helper_classes_have_valid_php_syntax()
    {
        $helperFiles = [
            'src/Helpers/DatabaseManager.php',
            'src/Helpers/EnvironmentManager.php',
            'src/Helpers/FinalInstallManager.php',
            'src/Helpers/InstalledFileManager.php',
            'src/Helpers/PermissionsChecker.php',
            'src/Helpers/RequirementsChecker.php',
        ];

        foreach ($helperFiles as $file) {
            $fullPath = __DIR__ . '/../' . $file;
            $output = shell_exec("php -l {$fullPath} 2>&1");
            $this->assertStringContainsString('No syntax errors', $output, "Syntax error in {$file}");
        }
    }

    public function test_event_classes_have_valid_php_syntax()
    {
        $eventFiles = [
            'src/Events/EnvironmentSaved.php',
            'src/Events/LaravelInstallerFinished.php',
        ];

        foreach ($eventFiles as $file) {
            $fullPath = __DIR__ . '/../' . $file;
            $output = shell_exec("php -l {$fullPath} 2>&1");
            $this->assertStringContainsString('No syntax errors', $output, "Syntax error in {$file}");
        }
    }

    public function test_helper_classes_have_correct_namespace()
    {
        $helperFiles = [
            'DatabaseManager' => 'src/Helpers/DatabaseManager.php',
            'EnvironmentManager' => 'src/Helpers/EnvironmentManager.php',
            'FinalInstallManager' => 'src/Helpers/FinalInstallManager.php',
            'InstalledFileManager' => 'src/Helpers/InstalledFileManager.php',
            'PermissionsChecker' => 'src/Helpers/PermissionsChecker.php',
            'RequirementsChecker' => 'src/Helpers/RequirementsChecker.php',
        ];

        foreach ($helperFiles as $className => $file) {
            $content = file_get_contents(__DIR__ . '/../' . $file);
            $this->assertStringContainsString('namespace NeuronicLab\Installer\Helpers;', $content, 
                "Incorrect namespace in {$file}");
            $this->assertStringContainsString("class {$className}", $content, 
                "Class {$className} not found in {$file}");
        }
    }

    public function test_event_classes_have_correct_namespace()
    {
        $eventFiles = [
            'EnvironmentSaved' => 'src/Events/EnvironmentSaved.php',
            'LaravelInstallerFinished' => 'src/Events/LaravelInstallerFinished.php',
        ];

        foreach ($eventFiles as $className => $file) {
            $content = file_get_contents(__DIR__ . '/../' . $file);
            $this->assertStringContainsString('namespace NeuronicLab\Installer\Events;', $content, 
                "Incorrect namespace in {$file}");
            $this->assertStringContainsString("class {$className}", $content, 
                "Class {$className} not found in {$file}");
        }
    }

    public function test_helper_classes_have_required_methods()
    {
        $requiredMethods = [
            'DatabaseManager' => ['migrateAndSeed'],
            'EnvironmentManager' => ['getEnvContent', 'saveFileWizard', 'saveFileClassic'],
            'FinalInstallManager' => ['runFinal'],
            'InstalledFileManager' => ['update'],
            'PermissionsChecker' => ['check'],
            'RequirementsChecker' => ['check', 'checkPHPversion'],
        ];

        foreach ($requiredMethods as $className => $methods) {
            $filePath = __DIR__ . "/../src/Helpers/{$className}.php";
            $content = file_get_contents($filePath);
            
            foreach ($methods as $method) {
                $this->assertStringContainsString("function {$method}(", $content, 
                    "Method {$method} not found in {$className}");
            }
        }
    }

    public function test_no_rachidlaasri_references_in_helpers()
    {
        $helperFiles = glob(__DIR__ . '/../src/Helpers/*.php');
        
        foreach ($helperFiles as $file) {
            $content = file_get_contents($file);
            $this->assertStringNotContainsString('RachidLaasri\\LaravelInstaller', $content, 
                "Found rachidlaasri reference in " . basename($file));
        }
    }

    public function test_no_rachidlaasri_references_in_events()
    {
        $eventFiles = glob(__DIR__ . '/../src/Events/*.php');
        
        foreach ($eventFiles as $file) {
            $content = file_get_contents($file);
            $this->assertStringNotContainsString('RachidLaasri\\LaravelInstaller', $content, 
                "Found rachidlaasri reference in " . basename($file));
        }
    }
}
