<?php

namespace NeuronicLab\Installer\Tests;

use PHPUnit\Framework\TestCase;

class IndependenceTest extends TestCase
{
    public function test_no_rachidlaasri_dependency_in_composer()
    {
        $composerJson = json_decode(file_get_contents(__DIR__ . '/../composer.json'), true);
        
        $this->assertArrayNotHasKey('rachidlaasri/laravel-installer', $composerJson['require'], 
            'rachidlaasri/laravel-installer dependency still exists in composer.json');
    }

    public function test_required_dependencies_exist()
    {
        $composerJson = json_decode(file_get_contents(__DIR__ . '/../composer.json'), true);
        
        $requiredDependencies = [
            'php' => '^8.1',
            'laravel/framework' => '^10.0',
            'nesbot/carbon' => '^2.0'
        ];

        foreach ($requiredDependencies as $package => $version) {
            $this->assertArrayHasKey($package, $composerJson['require'], 
                "Required dependency {$package} is missing");
        }
    }

    public function test_no_rachidlaasri_references_in_source_files()
    {
        $sourceFiles = $this->getAllPhpFiles(__DIR__ . '/../src');
        
        foreach ($sourceFiles as $file) {
            $content = file_get_contents($file);
            $this->assertStringNotContainsString('RachidLaasri\\LaravelInstaller', $content, 
                "Found rachidlaasri reference in " . $this->getRelativePath($file));
        }
    }

    public function test_no_rachidlaasri_references_in_documentation()
    {
        $docFiles = [
            __DIR__ . '/../README.md',
            __DIR__ . '/../CHANGELOG.md',
            __DIR__ . '/../docs/PUBLISHING-GUIDE.md',
        ];

        foreach ($docFiles as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                $this->assertStringNotContainsString('rachidlaasri/laravel-installer', $content, 
                    "Found rachidlaasri reference in " . basename($file));
            }
        }
    }

    public function test_all_custom_helper_classes_exist()
    {
        $requiredHelpers = [
            'DatabaseManager',
            'EnvironmentManager', 
            'FinalInstallManager',
            'InstalledFileManager',
            'PermissionsChecker',
            'RequirementsChecker'
        ];

        foreach ($requiredHelpers as $helper) {
            $filePath = __DIR__ . "/../src/Helpers/{$helper}.php";
            $this->assertFileExists($filePath, "Custom helper {$helper} does not exist");
        }
    }

    public function test_all_custom_event_classes_exist()
    {
        $requiredEvents = [
            'EnvironmentSaved',
            'LaravelInstallerFinished'
        ];

        foreach ($requiredEvents as $event) {
            $filePath = __DIR__ . "/../src/Events/{$event}.php";
            $this->assertFileExists($filePath, "Custom event {$event} does not exist");
        }
    }

    public function test_package_autoload_configuration()
    {
        $composerJson = json_decode(file_get_contents(__DIR__ . '/../composer.json'), true);
        
        $this->assertArrayHasKey('autoload', $composerJson);
        $this->assertArrayHasKey('psr-4', $composerJson['autoload']);
        $this->assertArrayHasKey('NeuronicLab\\Installer\\', $composerJson['autoload']['psr-4']);
        $this->assertEquals('src/', $composerJson['autoload']['psr-4']['NeuronicLab\\Installer\\']);
    }

    public function test_service_provider_configuration()
    {
        $composerJson = json_decode(file_get_contents(__DIR__ . '/../composer.json'), true);
        
        $this->assertArrayHasKey('extra', $composerJson);
        $this->assertArrayHasKey('laravel', $composerJson['extra']);
        $this->assertArrayHasKey('providers', $composerJson['extra']['laravel']);
        $this->assertContains('NeuronicLab\\Installer\\Providers\\InstallerServiceProvider', 
            $composerJson['extra']['laravel']['providers']);
    }

    public function test_translation_keys_exist()
    {
        $translationFile = __DIR__ . '/../resources/lang/en/installer_messages.php';
        $this->assertFileExists($translationFile, 'Translation file does not exist');
        
        $translations = include $translationFile;
        $this->assertIsArray($translations, 'Translation file does not return an array');
        
        // Test for new translation keys added for custom helpers
        $this->assertArrayHasKey('final', $translations);
        $this->assertArrayHasKey('log_file_created', $translations['final']);
        $this->assertArrayHasKey('log_file_updated', $translations['final']);
    }

    public function test_independence_verification_script_exists()
    {
        $scriptPath = __DIR__ . '/../test-independence.php';
        $this->assertFileExists($scriptPath, 'Independence verification script does not exist');
        
        $content = file_get_contents($scriptPath);
        $this->assertStringContainsString('Testing NeuronicLab Installer Independence', $content);
    }

    public function test_migration_documentation_exists()
    {
        $docPath = __DIR__ . '/../INDEPENDENCE-MIGRATION.md';
        $this->assertFileExists($docPath, 'Independence migration documentation does not exist');
        
        $content = file_get_contents($docPath);
        $this->assertStringContainsString('NeuronicLab Installer Independence Migration', $content);
    }

    public function test_changelog_documents_independence()
    {
        $changelogPath = __DIR__ . '/../CHANGELOG.md';
        $this->assertFileExists($changelogPath, 'CHANGELOG.md does not exist');
        
        $content = file_get_contents($changelogPath);
        $this->assertStringContainsString('Removed dependency on `rachidlaasri/laravel-installer`', $content);
        $this->assertStringContainsString('Self-contained', $content);
    }

    /**
     * Get all PHP files recursively from a directory
     */
    private function getAllPhpFiles($directory)
    {
        $files = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * Get relative path for better error messages
     */
    private function getRelativePath($file)
    {
        $basePath = __DIR__ . '/../';
        return str_replace($basePath, '', $file);
    }
}
