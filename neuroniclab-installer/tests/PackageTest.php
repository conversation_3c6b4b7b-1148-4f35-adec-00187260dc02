<?php

namespace NeuronicLab\Installer\Tests;

use PHPUnit\Framework\TestCase;

class PackageTest extends TestCase
{
    public function test_package_structure_exists()
    {
        $basePath = __DIR__ . '/..';

        // Test essential files exist
        $this->assertFileExists($basePath . '/composer.json');
        $this->assertFileExists($basePath . '/README.md');
        $this->assertFileExists($basePath . '/LICENSE');
        $this->assertFileExists($basePath . '/CHANGELOG.md');

        // Test configuration exists
        $this->assertFileExists($basePath . '/config/neuroniclab-installer.php');

        // Test source files exist
        $this->assertDirectoryExists($basePath . '/src/Controllers');
        $this->assertDirectoryExists($basePath . '/src/Middleware');
        $this->assertDirectoryExists($basePath . '/src/Providers');
        $this->assertDirectoryExists($basePath . '/src/Commands');
        $this->assertDirectoryExists($basePath . '/src/Helpers');
        $this->assertDirectoryExists($basePath . '/src/Events');

        // Test assets exist
        $this->assertDirectoryExists($basePath . '/assets');
        $this->assertDirectoryExists($basePath . '/assets/css');
        $this->assertDirectoryExists($basePath . '/assets/fonts');
        $this->assertDirectoryExists($basePath . '/assets/img');

        // Test views exist
        $this->assertDirectoryExists($basePath . '/resources/views');
        $this->assertFileExists($basePath . '/resources/views/welcome.blade.php');
        $this->assertFileExists($basePath . '/resources/views/license.blade.php');

        // Test language files exist
        $this->assertDirectoryExists($basePath . '/resources/lang');
        $this->assertFileExists($basePath . '/resources/lang/en/installer_messages.php');

        // Test database schema exists
        $this->assertFileExists($basePath . '/database/schema.sql');
    }
    
    public function test_composer_json_structure()
    {
        $composerJson = json_decode(file_get_contents(__DIR__ . '/../composer.json'), true);

        $this->assertEquals('neuroniclab/installer', $composerJson['name']);
        $this->assertEquals('library', $composerJson['type']);
        $this->assertArrayHasKey('autoload', $composerJson);
        $this->assertArrayHasKey('psr-4', $composerJson['autoload']);
        $this->assertArrayHasKey('NeuronicLab\\Installer\\', $composerJson['autoload']['psr-4']);

        // Test that rachidlaasri dependency is removed
        $this->assertArrayNotHasKey('rachidlaasri/laravel-installer', $composerJson['require']);

        // Test required dependencies exist
        $this->assertArrayHasKey('php', $composerJson['require']);
        $this->assertArrayHasKey('laravel/framework', $composerJson['require']);
        $this->assertArrayHasKey('nesbot/carbon', $composerJson['require']);
    }

    public function test_config_file_structure()
    {
        $config = include __DIR__ . '/../config/neuroniclab-installer.php';

        $this->assertIsArray($config);
        $this->assertArrayHasKey('core', $config);
        $this->assertArrayHasKey('requirements', $config);
        $this->assertArrayHasKey('permissions', $config);
        $this->assertArrayHasKey('license', $config);
        $this->assertArrayHasKey('database', $config);
        $this->assertArrayHasKey('ui', $config);
    }
}
