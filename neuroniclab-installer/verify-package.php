<?php

/**
 * Package Verification Script
 * 
 * This script verifies that all required files and directories
 * are present in the NeuronicLab Installer package.
 */

echo "NeuronicLab Installer Package Verification\n";
echo "==========================================\n\n";

$basePath = __DIR__;
$errors = [];
$warnings = [];

// Required files
$requiredFiles = [
    'composer.json',
    'README.md',
    'LICENSE',
    'CHANGELOG.md',
    'docs/INTEGRATION.md',
    'config/neuroniclab-installer.php',
    'src/Providers/InstallerServiceProvider.php',
    'src/Controllers/WelcomeController.php',
    'src/Controllers/LicenseController.php',
    'src/Controllers/EnvironmentController.php',
    'src/Controllers/RequirementsController.php',
    'src/Controllers/PermissionsController.php',
    'src/Controllers/DatabaseController.php',
    'src/Controllers/FinalController.php',
    'src/Middleware/CanInstall.php',
    'src/Middleware/Security.php',
    'src/Commands/InstallCommand.php',
    'src/Commands/PublishCommand.php',
    'src/Routes/web.php',
    'src/Helpers/functions.php',
    'resources/views/layouts/master.blade.php',
    'resources/views/welcome.blade.php',
    'resources/views/license.blade.php',
    'resources/views/finished.blade.php',
    'resources/lang/en/installer_messages.php',
    'database/schema.sql',
];

// Required directories
$requiredDirectories = [
    'src',
    'src/Controllers',
    'src/Middleware',
    'src/Providers',
    'src/Commands',
    'src/Routes',
    'src/Helpers',
    'config',
    'resources',
    'resources/views',
    'resources/views/layouts',
    'resources/lang',
    'resources/lang/en',
    'assets',
    'assets/css',
    'assets/fonts',
    'assets/img',
    'database',
];

echo "Checking required files...\n";
foreach ($requiredFiles as $file) {
    $fullPath = $basePath . '/' . $file;
    if (file_exists($fullPath)) {
        echo "✓ {$file}\n";
    } else {
        echo "✗ {$file} - MISSING\n";
        $errors[] = "Missing file: {$file}";
    }
}

echo "\nChecking required directories...\n";
foreach ($requiredDirectories as $directory) {
    $fullPath = $basePath . '/' . $directory;
    if (is_dir($fullPath)) {
        echo "✓ {$directory}/\n";
    } else {
        echo "✗ {$directory}/ - MISSING\n";
        $errors[] = "Missing directory: {$directory}";
    }
}

echo "\nChecking composer.json structure...\n";
if (file_exists($basePath . '/composer.json')) {
    $composerData = json_decode(file_get_contents($basePath . '/composer.json'), true);
    
    if ($composerData === null) {
        $errors[] = "Invalid JSON in composer.json";
    } else {
        $requiredKeys = ['name', 'type', 'autoload', 'require'];
        foreach ($requiredKeys as $key) {
            if (isset($composerData[$key])) {
                echo "✓ composer.json has '{$key}'\n";
            } else {
                echo "✗ composer.json missing '{$key}'\n";
                $errors[] = "Missing key in composer.json: {$key}";
            }
        }
        
        if (isset($composerData['name']) && $composerData['name'] === 'neuroniclab/installer') {
            echo "✓ Package name is correct\n";
        } else {
            $warnings[] = "Package name might be incorrect";
        }
    }
}

echo "\nChecking configuration file...\n";
if (file_exists($basePath . '/config/neuroniclab-installer.php')) {
    $config = include $basePath . '/config/neuroniclab-installer.php';
    
    if (is_array($config)) {
        $requiredConfigKeys = ['core', 'requirements', 'permissions', 'license', 'database', 'ui'];
        foreach ($requiredConfigKeys as $key) {
            if (isset($config[$key])) {
                echo "✓ Config has '{$key}' section\n";
            } else {
                echo "✗ Config missing '{$key}' section\n";
                $errors[] = "Missing config section: {$key}";
            }
        }
    } else {
        $errors[] = "Configuration file does not return an array";
    }
}

echo "\nChecking assets...\n";
$assetFiles = ['css/style.min.css', 'fonts', 'img'];
foreach ($assetFiles as $asset) {
    $fullPath = $basePath . '/assets/' . $asset;
    if (file_exists($fullPath) || is_dir($fullPath)) {
        echo "✓ assets/{$asset}\n";
    } else {
        echo "✗ assets/{$asset} - MISSING\n";
        $warnings[] = "Missing asset: {$asset}";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "VERIFICATION SUMMARY\n";
echo str_repeat("=", 50) . "\n";

if (empty($errors) && empty($warnings)) {
    echo "✅ ALL CHECKS PASSED! Package is ready for use.\n";
    exit(0);
} else {
    if (!empty($errors)) {
        echo "❌ ERRORS FOUND:\n";
        foreach ($errors as $error) {
            echo "  - {$error}\n";
        }
    }
    
    if (!empty($warnings)) {
        echo "⚠️  WARNINGS:\n";
        foreach ($warnings as $warning) {
            echo "  - {$warning}\n";
        }
    }
    
    if (!empty($errors)) {
        echo "\nPackage has critical issues that need to be fixed.\n";
        exit(1);
    } else {
        echo "\nPackage is mostly ready but has some warnings.\n";
        exit(0);
    }
}
